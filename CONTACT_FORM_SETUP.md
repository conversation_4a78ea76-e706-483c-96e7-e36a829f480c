# Contact Form Setup Guide

This guide explains how to set up the contact form to send submissions to your private email address.

## EmailJS Setup

1. **Create an EmailJS Account**
   - Go to [EmailJS website](https://www.emailjs.com/) and sign up for a free account
   - The free tier includes 200 emails per month

2. **Set Up an Email Service**
   - In the EmailJS dashboard, go to "Email Services" and click "Add New Service"
   - Choose your email provider (Gmail, Outlook, etc.)
   - Follow the authentication steps
   - Give your service a name (e.g., "Portfolio Contact Form")
   - Note down the Service ID

3. **Create an Email Template**
   - In the EmailJS dashboard, go to "Email Templates" and click "Create New Template"
   - Design your email template with the following variables:
     - `{{user_name}}` - The name of the person contacting you
     - `{{user_email}}` - The email of the person contacting you
     - `{{subject}}` - The subject of the message
     - `{{message}}` - The content of the message
     - `{{recipient_email}}` - Your private email (<<EMAIL>>)
     - `{{submission_time}}` - Timestamp of the submission
     - `{{source}}` - Source of the submission (Portfolio Website Contact Form)

   - Example template:

     ```plaintext
     Subject: New Contact Form Submission: {{subject}}

     From: {{user_name}} ({{user_email}})
     Subject: {{subject}}
     Time: {{submission_time}}
     Source: {{source}}

     Message:
     {{message}}
     ```

   - Save the template and note down the Template ID

4. **Get Your Public Key**
   - In the EmailJS dashboard, go to "Account" > "API Keys"
   - Copy your Public Key

5. **Update Your Configuration File**
   - Open `src/config/emailjs.js` in your project
   - Replace the placeholder values with your actual credentials:

     ```javascript
     export const EMAILJS_CONFIG = {
       serviceId: 'your_service_id_here',
       templateId: 'your_template_id_here',
       publicKey: 'your_public_key_here',
       recipientEmail: '<EMAIL>', // Already set
     };
     ```

## Testing Before Deployment

1. **Local Testing**
   - Run your website locally with `npm run dev`
   - Navigate to the Contact page
   - Fill out the form with test data
   - Submit the form
   - Check your email (<<EMAIL>>) to confirm you received the test message
   - Verify all form fields are included in the email

2. **Production Testing**
   - After deploying your website, test the contact form again
   - Submit a test message from the live site
   - Confirm that you receive the email at <<EMAIL>>

## Privacy Considerations

- Your email address (<<EMAIL>>) is not visible to website visitors
- It's only used as the recipient for form submissions
- The email address is stored in the configuration file and passed as a hidden field in the form

## Troubleshooting

If you encounter any issues:

1. **Emails not being sent**:
   - Double-check your Service ID, Template ID, and Public Key
   - Ensure your email service is properly connected
   - Check the browser console for any errors

2. **Form submission errors**:
   - Look at the error message in the console
   - Verify that your template variables match the form field names

3. **Rate limiting**:
   - The free tier has a limit of 200 emails per month
   - Consider upgrading if you need more

## Security Recommendations

For added security:

1. **Add reCAPTCHA**:
   - EmailJS supports Google reCAPTCHA integration
   - This helps prevent spam submissions

2. **Use environment variables** (for production):
   - Create a `.env` file for your EmailJS credentials
   - Update your config to use `import.meta.env.VITE_EMAILJS_SERVICE_ID`, etc.
   - Add `.env` to your `.gitignore` file
