# <PERSON><PERSON><PERSON><PERSON><PERSON> Mohite Portfolio Website

This is the production build and deployment guide for <PERSON><PERSON><PERSON><PERSON><PERSON> Mohite's professional portfolio website.

## Project Overview

The website showcases <PERSON><PERSON><PERSON><PERSON><PERSON>'s work as an entrepreneur, AI strategist, full-stack developer, and digital designer. It features a responsive design with dark/light theme toggle, interactive elements, and optimized performance.

## Prerequisites

Before building and deploying the website, ensure you have:

1. Node.js (v16 or higher) installed
2. NPM (v8 or higher) installed
3. A Hostinger hosting account
4. A domain name (either through Hostinger or external)

## Email Configuration

The contact form is configured using EmailJS, a service that allows sending emails directly from client-side JavaScript without requiring a server. EmailJS handles form submissions and delivers messages to the specified recipient email address.

The configuration is stored in `src/config/emailjs.js`:

```javascript
// EmailJS configuration
export const EMAILJS_CONFIG = {
  serviceId: 'service_dgqapei',
  templateId: 'template_drqdeln',
  publicKey: '3BuxpD88IbzmdY6Ov',
  recipientEmail: '<EMAIL>' // Email for form submissions
};
```

These parameters are used in the ContactForm component to initialize EmailJS and send form submissions to the specified recipient email address.

## Building for Production

The project includes a comprehensive production build script that handles various optimization tasks:

1. **Run the production build script**

   ```bash
   npm run build:prod
   ```

   This script performs the following tasks:
   - Cleans previous build files
   - Optimizes all images (compression, WebP conversion)
   - Generates favicon.ico with multiple sizes
   - Creates an optimized production build with code splitting
   - Generates a service worker for offline capabilities
   - Creates gzip and Brotli compressed versions of assets

2. **Individual optimization commands**

   ```bash
   # Optimize images only
   npm run optimize:images

   # Optimize profile images only
   npm run optimize:profile

   # Optimize large project images only
   npm run optimize:projects

   # Optimize problematic images only
   npm run optimize:problematic

   # Generate favicon
   npm run create:favicon

   # Analyze bundle size
   npm run analyze

   # Preview production build
   npm run preview

   # Run Lighthouse audit
   npm run lighthouse
   ```

## Deployment to Hostinger

### Step-by-Step Deployment Process

1. **Build the project for production**

   ```bash
   npm run build:prod
   ```

   This will create a `dist` folder containing all optimized files.

2. **Log in to Hostinger Control Panel**
   - Go to [https://hpanel.hostinger.com/](https://hpanel.hostinger.com/)
   - Log in with your Hostinger account credentials

3. **Set up your domain**
   - If you purchased your domain through Hostinger, it should be automatically set up
   - If using an external domain, add it to Hostinger and update nameservers at your domain registrar
   - Navigate to "Domains" → "Manage" → "DNS Zone Editor" to verify DNS settings

4. **Upload website files**

   **Option 1: Using File Manager**
   - In Hostinger Control Panel, go to "Files" → "File Manager"
   - Navigate to the `public_html` directory
   - Remove any default files (like index.html, index.php)
   - Click "Upload" and select all files from your local `dist` folder

   **Option 2: Using FTP**
   - Get FTP credentials from Hostinger Control Panel ("Files" → "FTP Accounts")
   - Connect to your server using an FTP client like FileZilla
   - Upload all files from your local `dist` folder to the `public_html` directory

5. **Configure URL rewriting for SPA routing**
   - Create a `.htaccess` file in the `public_html` directory with the following content:

     ```apache
     <IfModule mod_rewrite.c>
       RewriteEngine On
       RewriteBase /
       RewriteRule ^index\.html$ - [L]
       RewriteCond %{REQUEST_FILENAME} !-f
       RewriteCond %{REQUEST_FILENAME} !-d
       RewriteRule . /index.html [L]
     </IfModule>

     # Caching rules
     <IfModule mod_expires.c>
       ExpiresActive On

       # Images
       ExpiresByType image/jpeg "access plus 1 year"
       ExpiresByType image/gif "access plus 1 year"
       ExpiresByType image/png "access plus 1 year"
       ExpiresByType image/webp "access plus 1 year"
       ExpiresByType image/svg+xml "access plus 1 year"
       ExpiresByType image/x-icon "access plus 1 year"

       # CSS, JavaScript
       ExpiresByType text/css "access plus 1 month"
       ExpiresByType text/javascript "access plus 1 month"
       ExpiresByType application/javascript "access plus 1 month"

       # Fonts
       ExpiresByType font/ttf "access plus 1 year"
       ExpiresByType font/otf "access plus 1 year"
       ExpiresByType font/woff "access plus 1 year"
       ExpiresByType font/woff2 "access plus 1 year"
     </IfModule>
     ```

6. **Set up SSL Certificate**
   - In Hostinger Control Panel, go to "SSL/TLS" section
   - Click "Setup" next to your domain
   - Select "Free SSL by Let's Encrypt" (or use a custom certificate if you have one)
   - Click "Install" and wait for the certificate to be issued
   - Enable "Force HTTPS" to redirect all HTTP traffic to HTTPS

7. **Verify deployment**
   - Visit your website at your domain (e.g., `https://www.hrishikeshmohite.com`)
   - Test navigation to ensure all routes work correctly
   - Test the contact form to ensure EmailJS is working properly (see the Email Configuration section for details)
   - Submit a test message through the contact form to verify it's delivered to the configured recipient email
   - Verify that the website displays correctly on different devices and browsers

### Environment Configurations for Hostinger

Hostinger uses Apache as its web server. The following configurations are important:

1. **PHP Version**
   - Although this is a static site, if you need PHP for any server-side functionality:
   - Go to "Advanced" → "PHP Configuration"
   - Set PHP version to 8.1 or higher

2. **Memory Limits**
   - Default settings should be sufficient for a static site
   - If needed, you can adjust PHP memory limits in the PHP Configuration section

3. **CORS Headers**
   - If your site makes API calls to external services, you may need to configure CORS
   - Add the following to your `.htaccess` file:

     ```apache
     <IfModule mod_headers.c>
       Header set Access-Control-Allow-Origin "*"
       Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
       Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
     </IfModule>
     ```

4. **Compression**
   - Enable Gzip compression in `.htaccess`:

     ```apache
     <IfModule mod_deflate.c>
       AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
     </IfModule>
     ```

### Troubleshooting Common Issues

1. **404 errors when refreshing pages**
   - Verify that the `.htaccess` file is correctly uploaded and contains the rewrite rules
   - Check if mod_rewrite is enabled on your hosting (it should be on Hostinger)

2. **Contact form not working**
   - Verify that EmailJS is correctly configured in `src/config/emailjs.js` with the proper service ID, template ID, public key, and recipient email
   - Ensure the EmailJS service is active in your EmailJS dashboard
   - Check browser console for any JavaScript errors
   - Verify that the form is properly connected to the EmailJS service in the ContactForm component

3. **SSL certificate issues**
   - Wait up to 24 hours for SSL certificate propagation
   - Ensure "Force HTTPS" is enabled in Hostinger SSL settings

4. **Slow loading times**
   - Run a Lighthouse audit to identify performance bottlenecks
   - Consider enabling Hostinger's LiteSpeed Cache if available
   - Verify that images are properly optimized
