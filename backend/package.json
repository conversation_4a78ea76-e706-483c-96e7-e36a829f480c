{"name": "hris<PERSON><PERSON><PERSON>-mohite-elearning-backend", "version": "1.0.0", "description": "Backend API for Hrishikesh Mohite E-Learning Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "node prisma/seed.js"}, "keywords": ["elearning", "education", "courses", "api", "nodejs", "express", "prisma", "postgresql"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.41.0", "nodemailer": "^6.9.7", "razorpay": "^2.9.2", "stripe": "^14.9.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cookie-parser": "^1.4.6", "compression": "^1.7.4", "express-slow-down": "^2.0.1", "redis": "^4.6.11", "ioredis": "^5.3.2", "uuid": "^9.0.1", "crypto": "^1.0.1", "sharp": "^0.33.1", "pdf-parse": "^1.1.1", "node-cron": "^3.0.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/node": "^20.10.5", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}