// This script prepares and builds the project for production
// Run with: node build-production.cjs

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  // Whether to optimize images before building
  optimizeImages: true,
  // Whether to generate favicon.ico
  generateFavicon: true,
  // Whether to run tests before building
  runTests: false,
  // Whether to analyze bundle size
  analyzeBundleSize: true,
  // Whether to generate a service worker
  generateServiceWorker: true,
  // Whether to create a gzip version of assets
  createGzipAssets: true,
  // Whether to create a Brotli version of assets
  createBrotliAssets: true,
};

// Function to execute a command and log output
function executeCommand(command, description) {
  console.log(`\n📋 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Function to check if a package is installed
function isPackageInstalled(packageName) {
  try {
    require.resolve(packageName);
    return true;
  } catch (error) {
    return false;
  }
}

// Function to install a package if not already installed
function installPackageIfNeeded(packageName, isDev = false) {
  if (!isPackageInstalled(packageName)) {
    console.log(`📦 Installing ${packageName}...`);
    const devFlag = isDev ? '--save-dev' : '--save';
    execSync(`npm install ${packageName} ${devFlag}`, { stdio: 'inherit' });
    console.log(`✅ Installed ${packageName}`);
  }
}

// Main function
async function main() {
  console.log('🚀 Starting production build process...');

  // Install required packages
  console.log('\n📦 Checking required packages...');

  if (config.optimizeImages) {
    installPackageIfNeeded('sharp', true);
  }

  if (config.analyzeBundleSize) {
    installPackageIfNeeded('rollup-plugin-visualizer', true);
  }

  // Disabled service worker generation
  config.generateServiceWorker = false;

  if (config.createGzipAssets || config.createBrotliAssets) {
    installPackageIfNeeded('compression-webpack-plugin', true);
  }

  // Clean previous build
  try {
    console.log('\n📋 Cleaning previous build...');
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    console.log('✅ Cleaning previous build completed successfully');
  } catch (error) {
    console.error('❌ Cleaning previous build failed:', error.message);
  }

  // Optimize images if configured
  if (config.optimizeImages) {
    if (fs.existsSync('optimize-images.js')) {
      // First optimize profile images to ensure color fidelity
      executeCommand('node optimize-images.js --profile-only', 'Optimizing profile images with maximum color fidelity');

      // Then optimize large project images separately
      executeCommand('node optimize-images.js --project-only', 'Optimizing large project images with high color fidelity');

      // Then optimize problematic images separately
      executeCommand('node optimize-images.js --problematic-only', 'Optimizing problematic images with high color fidelity');

      // Then optimize all other images
      executeCommand('node optimize-images.js', 'Optimizing remaining images with color fidelity preservation');

      // Verify color fidelity for profile images
      console.log('\n🔍 Verifying color fidelity for profile images...');
      try {
        // Check if sharp is installed
        try {
          require.resolve('sharp');
        } catch (error) {
          console.log('📦 Installing sharp package...');
          execSync('npm install sharp --save-dev', { stdio: 'inherit' });
        }

        // Simple verification that optimized images exist and have reasonable file sizes
        const profileImages = [
          'src/assets/images/hero/optimized/Hrishikesh-Mohite-professional-portrait.png',
          'src/assets/images/hero/optimized/Hrishikesh-Mohite-professional-portrait.webp'
        ];

        for (const imagePath of profileImages) {
          if (fs.existsSync(imagePath)) {
            const stats = fs.statSync(imagePath);
            // Check that file size is reasonable (not too small which might indicate loss of data)
            if (stats.size < 100000) { // Less than 100KB might indicate too much compression
              console.warn(`⚠️ Warning: ${imagePath} might be over-compressed (${Math.round(stats.size/1024)}KB)`);
            } else {
              console.log(`✅ Verified ${imagePath} (${Math.round(stats.size/1024)}KB)`);
            }
          } else {
            console.warn(`⚠️ Warning: ${imagePath} not found`);
          }
        }
      } catch (error) {
        console.warn(`⚠️ Warning: Could not verify color fidelity: ${error.message}`);
      }
    } else {
      console.warn('⚠️ optimize-images.js not found, skipping image optimization');
    }
  }

  // Generate favicon.ico if configured
  if (config.generateFavicon) {
    if (fs.existsSync('create-favicon-ico.js')) {
      // Make sure to-ico is installed
      try {
        require.resolve('to-ico');
      } catch (error) {
        console.log('📦 Installing to-ico package...');
        execSync('npm install to-ico --save-dev', { stdio: 'inherit' });
      }

      executeCommand('node create-favicon-ico.js', 'Generating favicon.ico');
    } else {
      console.warn('⚠️ create-favicon-ico.js not found, skipping favicon generation');
    }
  }

  // Run tests if configured
  if (config.runTests) {
    executeCommand('npm test', 'Running tests');
  }

  // Set environment variables for production build
  process.env.NODE_ENV = 'production';

  // Set environment variable to disable imagemin warnings
  process.env.VITE_DISABLE_IMAGEMIN_WARNINGS = 'true';

  // Add bundle analyzer if configured
  if (config.analyzeBundleSize) {
    // Use the analyze mode directly instead of modifying the config
    executeCommand('vite build --mode analyze', 'Building for production with bundle analysis');
  } else {
    // Regular build
    executeCommand('vite build', 'Building for production');
  }

  // Service worker generation has been disabled
  console.log('ℹ️ Service worker generation is disabled to prevent PWA installation prompts');

  // Create compressed assets if configured
  if (config.createGzipAssets || config.createBrotliAssets) {
    console.log('\n📦 Creating compressed assets...');

    // Find all JS and CSS files in dist
    const distDir = path.join(__dirname, 'dist');
    const findAssets = (dir, ext) => {
      const results = [];
      const files = fs.readdirSync(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          results.push(...findAssets(filePath, ext));
        } else if (file.endsWith(ext)) {
          results.push(filePath);
        }
      }

      return results;
    };

    const jsFiles = findAssets(distDir, '.js');
    const cssFiles = findAssets(distDir, '.css');
    const htmlFiles = findAssets(distDir, '.html');
    const allAssets = [...jsFiles, ...cssFiles, ...htmlFiles];

    // Create gzip versions
    if (config.createGzipAssets) {
      const zlib = require('zlib');

      for (const asset of allAssets) {
        const content = fs.readFileSync(asset);
        const gzipped = zlib.gzipSync(content, { level: 9 });
        fs.writeFileSync(`${asset}.gz`, gzipped);
      }

      console.log(`✅ Created gzip versions of ${allAssets.length} assets`);
    }

    // Create brotli versions
    if (config.createBrotliAssets) {
      const zlib = require('zlib');

      for (const asset of allAssets) {
        const content = fs.readFileSync(asset);
        const brotlied = zlib.brotliCompressSync(content, {
          params: {
            [zlib.constants.BROTLI_PARAM_QUALITY]: 11,
          },
        });
        fs.writeFileSync(`${asset}.br`, brotlied);
      }

      console.log(`✅ Created brotli versions of ${allAssets.length} assets`);
    }
  }

  console.log('\n🎉 Production build completed successfully!');
  console.log('📁 Output directory: dist/');
}

// Run the script
main().catch(error => {
  console.error('❌ Build failed:', error);
  process.exit(1);
});
