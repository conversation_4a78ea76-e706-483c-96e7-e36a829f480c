// This script creates a favicon.ico file using sharp and to-ico
// Run with: node create-favicon-ico.js

/* eslint-env node */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';
import toIco from 'to-ico';

// Get the directory name (equivalent to __dirname in CommonJS)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Paths
const sourceImagePath = path.join(__dirname, 'public/favicon-32x32.svg');
const targetImagePath = path.join(__dirname, 'public/favicon.ico');
const tempPngPath = path.join(__dirname, 'public/temp-favicon.png');

// Check if source image exists
if (!fs.existsSync(sourceImagePath)) {
  console.error(`Source image not found: ${sourceImagePath}`);
  process.exit(1);
}

console.log('Creating favicon.ico...');

// Function to create PNG of specific size
async function createSizedPng(size) {
  const outputPath = path.join(__dirname, `public/favicon-${size}x${size}.png`);

  try {
    await sharp(sourceImagePath)
      .resize(size, size)
      .png()
      .toFile(outputPath);

    console.log(`Created ${size}x${size} PNG: ${outputPath}`);
    return outputPath;
  } catch (err) {
    console.error(`Error creating ${size}x${size} PNG:`, err);
    throw err;
  }
}

// Main function to create favicon.ico
async function createFavicon() {
  try {
    // Create PNGs of different sizes
    const png16 = await createSizedPng(16);
    const png32 = await createSizedPng(32);
    const png48 = await createSizedPng(48);

    // Read the PNG files
    const png16Buffer = fs.readFileSync(png16);
    const png32Buffer = fs.readFileSync(png32);
    const png48Buffer = fs.readFileSync(png48);

    // Create the ICO file with multiple sizes
    const icoBuffer = await toIco([png16Buffer, png32Buffer, png48Buffer], {
      sizes: [16, 32, 48],
      resize: true
    });

    // Write the ICO file
    fs.writeFileSync(targetImagePath, icoBuffer);

    console.log(`Favicon.ico created successfully: ${targetImagePath}`);

    // Clean up temporary files
    fs.unlinkSync(png16);
    fs.unlinkSync(png32);
    fs.unlinkSync(png48);

    return true;
  } catch (err) {
    console.error('Error creating favicon.ico:', err);
    return false;
  }
}

// Run the main function
createFavicon()
  .then(success => {
    if (success) {
      console.log('Done!');
    } else {
      console.error('Failed to create favicon.ico');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
