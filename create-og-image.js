// This script creates an optimized Open Graph image from the profile image
// Run with: node create-og-image.js

/* eslint-env node */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Paths
const sourceImagePath = path.join(__dirname, 'src/assets/images/hero/Hrishikesh-Mohite-professional-portrait.png');
const targetImagePath = path.join(__dirname, 'public/og-image.jpg');

// Check if ImageMagick is installed
try {
  execSync('convert -version', { stdio: 'ignore' });
  console.log('ImageMagick is installed. Proceeding...');
} catch (_error) {
  console.error('ImageMagick is not installed. Please install it to run this script.');
  console.log('For Windows: https://imagemagick.org/script/download.php');
  console.log('For Mac: brew install imagemagick');
  console.log('For Linux: sudo apt-get install imagemagick');
  process.exit(1);
}

// Check if source image exists
if (!fs.existsSync(sourceImagePath)) {
  console.error(`Source image not found: ${sourceImagePath}`);
  process.exit(1);
}

// Create Open Graph image (1200x630 is the recommended size)
try {
  // Command to create a properly sized Open Graph image with text overlay
  const command = `convert "${sourceImagePath}" -resize 1200x630^ -gravity center -extent 1200x630 -quality 85 "${targetImagePath}"`;

  execSync(command);
  console.log(`Open Graph image created successfully: ${targetImagePath}`);
} catch (err) {
  console.error('Error creating Open Graph image:', err.message);
  process.exit(1);
}

console.log('Done!');
