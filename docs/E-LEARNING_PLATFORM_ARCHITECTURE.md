# E-Learning Platform Architecture Plan

## Overview
Transform the existing Courses page into a comprehensive e-learning platform while maintaining the current React/Vite architecture and design consistency.

## Technology Stack Recommendations

### Frontend (Existing + Enhancements)
- **Framework**: React 19.1.0 (Current)
- **Build Tool**: Vite 6.3.5 (Current)
- **Routing**: React Router DOM 7.6.0 (Current)
- **Styling**: CSS Modules + CSS Variables (Current)
- **Icons**: React Icons 5.5.0 (Current)
- **Animations**: Framer Motion 12.12.1 (Current)
- **New Additions**:
  - React Hook Form (form management)
  - React Query/TanStack Query (server state management)
  - React Player (video playback)
  - React PDF (PDF viewer)
  - Zustand (client state management)

### Backend (New Implementation)
- **Runtime**: Node.js with Express.js
- **Database**: PostgreSQL (primary) + Redis (caching/sessions)
- **ORM**: Prisma (type-safe database access)
- **Authentication**: JWT + bcrypt
- **File Storage**: AWS S3 or Cloudinary (course content)
- **Email Service**: EmailJS (current) + Nodemailer for transactional emails

### Payment Gateway
- **Primary**: Razorpay (Indian market focus)
- **Backup**: Stripe (international support)
- **Features**: INR + multi-currency support, webhooks, refunds

### Security & Infrastructure
- **SSL/TLS**: Let's Encrypt
- **CDN**: Cloudflare (existing setup)
- **Hosting**: Vercel (frontend) + Railway/Render (backend)
- **Environment**: Docker containers for backend
- **Monitoring**: Sentry for error tracking

## Database Schema Design

### Core Tables

#### Users
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  role ENUM('student', 'instructor', 'admin') DEFAULT 'student',
  email_verified BOOLEAN DEFAULT false,
  email_verification_token VARCHAR(255),
  password_reset_token VARCHAR(255),
  password_reset_expires TIMESTAMP,
  profile_image_url TEXT,
  phone VARCHAR(20),
  country VARCHAR(100),
  timezone VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Courses
```sql
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT NOT NULL,
  short_description TEXT,
  instructor_id UUID REFERENCES users(id),
  category_id UUID REFERENCES categories(id),
  level ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'INR',
  duration_weeks INTEGER,
  thumbnail_url TEXT,
  preview_video_url TEXT,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  featured BOOLEAN DEFAULT false,
  max_students INTEGER,
  prerequisites TEXT[],
  learning_outcomes TEXT[],
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Course Modules & Lessons
```sql
CREATE TABLE course_modules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  order_index INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE lessons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  module_id UUID REFERENCES course_modules(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  content_type ENUM('video', 'text', 'quiz', 'assignment', 'resource') NOT NULL,
  content_url TEXT,
  content_text TEXT,
  duration_minutes INTEGER,
  order_index INTEGER NOT NULL,
  is_preview BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Enrollments & Progress
```sql
CREATE TABLE enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  course_id UUID REFERENCES courses(id),
  enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP,
  progress_percentage DECIMAL(5,2) DEFAULT 0,
  certificate_issued BOOLEAN DEFAULT false,
  certificate_url TEXT,
  UNIQUE(user_id, course_id)
);

CREATE TABLE lesson_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  enrollment_id UUID REFERENCES enrollments(id),
  lesson_id UUID REFERENCES lessons(id),
  completed BOOLEAN DEFAULT false,
  completed_at TIMESTAMP,
  watch_time_seconds INTEGER DEFAULT 0,
  UNIQUE(enrollment_id, lesson_id)
);
```

#### Payments
```sql
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  course_id UUID REFERENCES courses(id),
  payment_gateway VARCHAR(50) NOT NULL,
  gateway_payment_id VARCHAR(255) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) NOT NULL,
  status ENUM('pending', 'completed', 'failed', 'refunded') NOT NULL,
  payment_method VARCHAR(50),
  gateway_response JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## System Architecture

### Frontend Architecture
```
src/
├── components/
│   ├── auth/           # Login, Register, Profile components
│   ├── course/         # Course display, player, progress
│   ├── dashboard/      # Student and admin dashboards
│   ├── payment/        # Payment forms and confirmation
│   └── ui/            # Existing UI components
├── pages/
│   ├── auth/          # Authentication pages
│   ├── dashboard/     # Dashboard pages
│   ├── course/        # Course detail and learning pages
│   └── admin/         # Admin panel pages
├── hooks/             # Custom React hooks
├── services/          # API calls and external services
├── store/             # Zustand state management
├── utils/             # Utility functions
└── types/             # TypeScript type definitions
```

### Backend Architecture
```
backend/
├── src/
│   ├── controllers/   # Route handlers
│   ├── middleware/    # Auth, validation, error handling
│   ├── models/        # Prisma models
│   ├── routes/        # API routes
│   ├── services/      # Business logic
│   ├── utils/         # Helper functions
│   └── config/        # Configuration files
├── prisma/            # Database schema and migrations
├── uploads/           # Temporary file storage
└── tests/             # Unit and integration tests
```

## Security Considerations

### Authentication & Authorization
- JWT tokens with short expiration (15 minutes) + refresh tokens
- Password hashing with bcrypt (12+ rounds)
- Email verification for new accounts
- Rate limiting on authentication endpoints
- Role-based access control (RBAC)

### Data Protection
- Input validation and sanitization
- SQL injection prevention (Prisma ORM)
- XSS protection with Content Security Policy
- CORS configuration for API endpoints
- Secure file upload with type validation

### Payment Security
- PCI DSS compliance through payment gateway
- Webhook signature verification
- Secure storage of payment metadata only
- No storage of sensitive payment data

## Implementation Phases

### Phase 1: Foundation (Weeks 1-2)
- Backend API setup with authentication
- Database schema implementation
- Basic user registration/login
- Admin panel foundation

### Phase 2: Course Management (Weeks 3-4)
- Course CRUD operations
- File upload system
- Course content organization
- Basic course display

### Phase 3: Payment Integration (Weeks 5-6)
- Razorpay integration
- Payment flow implementation
- Enrollment system
- Receipt generation

### Phase 4: Learning Experience (Weeks 7-8)
- Video player implementation
- Progress tracking
- Student dashboard
- Certificate generation

### Phase 5: Advanced Features (Weeks 9-10)
- Email notifications
- Analytics dashboard
- Mobile optimization
- Performance optimization

### Phase 6: Testing & Deployment (Weeks 11-12)
- Comprehensive testing
- Security audit
- Production deployment
- Monitoring setup

## Estimated Costs

### Development Tools (One-time)
- Domain SSL certificate: Free (Let's Encrypt)
- Development tools: Free (open source)

### Monthly Operational Costs
- Database hosting (PostgreSQL): $10-25/month
- Backend hosting: $10-20/month
- File storage (AWS S3): $5-15/month
- CDN (Cloudflare): Free tier initially
- Email service: $10-20/month
- Payment gateway fees: 2-3% per transaction

### Total Estimated Monthly Cost: $35-80

## Next Steps
1. Set up development environment
2. Initialize backend project structure
3. Implement database schema
4. Create authentication system
5. Build admin dashboard foundation
