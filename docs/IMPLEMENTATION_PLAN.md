# E-Learning Platform Implementation Plan

## Recommended Technology Choices

### Payment Gateway: Razorpay
**Why Razorpay over others:**
- **Indian Market Focus**: Best for Indian customers with UPI, cards, net banking
- **International Support**: Accepts international cards and PayPal
- **Competitive Fees**: 2% for domestic, 3% for international
- **Developer Friendly**: Excellent documentation and React SDK
- **Compliance**: PCI DSS compliant, handles all security
- **Features**: Instant refunds, subscription billing, detailed analytics

### Database: PostgreSQL + Redis
**Why PostgreSQL:**
- **ACID Compliance**: Ensures data integrity for payments
- **JSON Support**: Flexible for course content metadata
- **Scalability**: Handles complex queries and relationships
- **Free & Open Source**: No licensing costs

**Redis for:**
- Session management
- Caching frequently accessed course data
- Rate limiting implementation

## Step-by-Step Implementation Guide

### Step 1: Backend Foundation Setup

#### 1.1 Initialize Backend Project
```bash
mkdir backend
cd backend
npm init -y
npm install express cors helmet morgan dotenv bcryptjs jsonwebtoken
npm install prisma @prisma/client multer cloudinary nodemailer
npm install razorpay stripe express-rate-limit express-validator
npm install -D nodemon typescript @types/node @types/express
```

#### 1.2 Project Structure
```
backend/
├── src/
│   ├── config/
│   │   ├── database.js
│   │   ├── cloudinary.js
│   │   └── email.js
│   ├── controllers/
│   │   ├── authController.js
│   │   ├── courseController.js
│   │   ├── paymentController.js
│   │   └── userController.js
│   ├── middleware/
│   │   ├── auth.js
│   │   ├── validation.js
│   │   └── upload.js
│   ├── routes/
│   │   ├── auth.js
│   │   ├── courses.js
│   │   ├── payments.js
│   │   └── users.js
│   ├── services/
│   │   ├── emailService.js
│   │   ├── paymentService.js
│   │   └── fileService.js
│   └── app.js
├── prisma/
│   └── schema.prisma
└── server.js
```

### Step 2: Database Schema Implementation

#### 2.1 Prisma Schema Setup
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                    String    @id @default(cuid())
  email                 String    @unique
  passwordHash          String
  firstName             String
  lastName              String
  role                  Role      @default(STUDENT)
  emailVerified         Boolean   @default(false)
  emailVerificationToken String?
  passwordResetToken    String?
  passwordResetExpires  DateTime?
  profileImageUrl       String?
  phone                 String?
  country               String?
  timezone              String?
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  
  // Relations
  enrollments           Enrollment[]
  payments              Payment[]
  createdCourses        Course[]  @relation("CourseInstructor")
  
  @@map("users")
}

enum Role {
  STUDENT
  INSTRUCTOR
  ADMIN
}

model Course {
  id                String      @id @default(cuid())
  title             String
  slug              String      @unique
  description       String
  shortDescription  String?
  instructorId      String
  categoryId        String
  level             Level
  price             Decimal
  originalPrice     Decimal?
  currency          String      @default("INR")
  durationWeeks     Int?
  thumbnailUrl      String?
  previewVideoUrl   String?
  status            CourseStatus @default(DRAFT)
  featured          Boolean     @default(false)
  maxStudents       Int?
  prerequisites     String[]
  learningOutcomes  String[]
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  
  // Relations
  instructor        User        @relation("CourseInstructor", fields: [instructorId], references: [id])
  category          Category    @relation(fields: [categoryId], references: [id])
  modules           CourseModule[]
  enrollments       Enrollment[]
  payments          Payment[]
  
  @@map("courses")
}

enum Level {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum CourseStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

model Category {
  id          String   @id @default(cuid())
  name        String   @unique
  slug        String   @unique
  description String?
  createdAt   DateTime @default(now())
  
  courses     Course[]
  
  @@map("categories")
}
```

### Step 3: Authentication System

#### 3.1 JWT Authentication Middleware
```javascript
// src/middleware/auth.js
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: { id: true, email: true, role: true, emailVerified: true }
    });

    if (!user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};

const requireRole = (roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

module.exports = { authenticateToken, requireRole };
```

### Step 4: Payment Integration

#### 4.1 Razorpay Setup
```javascript
// src/services/paymentService.js
const Razorpay = require('razorpay');
const crypto = require('crypto');

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
});

class PaymentService {
  async createOrder(amount, currency = 'INR', courseId, userId) {
    try {
      const order = await razorpay.orders.create({
        amount: amount * 100, // Convert to paise
        currency,
        receipt: `course_${courseId}_user_${userId}_${Date.now()}`,
        notes: {
          courseId,
          userId,
        },
      });

      return order;
    } catch (error) {
      throw new Error(`Payment order creation failed: ${error.message}`);
    }
  }

  verifyPaymentSignature(orderId, paymentId, signature) {
    const body = orderId + '|' + paymentId;
    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET)
      .update(body.toString())
      .digest('hex');

    return expectedSignature === signature;
  }

  async processRefund(paymentId, amount) {
    try {
      const refund = await razorpay.payments.refund(paymentId, {
        amount: amount * 100, // Convert to paise
      });

      return refund;
    } catch (error) {
      throw new Error(`Refund processing failed: ${error.message}`);
    }
  }
}

module.exports = new PaymentService();
```

### Step 5: Frontend Integration

#### 5.1 Install Required Packages
```bash
npm install @tanstack/react-query zustand react-hook-form
npm install react-player react-pdf axios
npm install @hookform/resolvers yup
```

#### 5.2 Authentication Store (Zustand)
```javascript
// src/store/authStore.js
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      
      login: (user, token) => {
        set({ user, token, isAuthenticated: true });
        localStorage.setItem('token', token);
      },
      
      logout: () => {
        set({ user: null, token: null, isAuthenticated: false });
        localStorage.removeItem('token');
      },
      
      updateUser: (userData) => {
        set({ user: { ...get().user, ...userData } });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        token: state.token, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);

export default useAuthStore;
```

### Step 6: Course Player Component

#### 6.1 Video Player with Progress Tracking
```jsx
// src/components/course/VideoPlayer.jsx
import React, { useState, useRef } from 'react';
import ReactPlayer from 'react-player';
import { useMutation } from '@tanstack/react-query';
import { updateLessonProgress } from '../../services/courseService';

const VideoPlayer = ({ lesson, enrollmentId, onProgress }) => {
  const [played, setPlayed] = useState(0);
  const [duration, setDuration] = useState(0);
  const playerRef = useRef(null);

  const progressMutation = useMutation({
    mutationFn: updateLessonProgress,
    onSuccess: () => {
      onProgress && onProgress(lesson.id, played);
    },
  });

  const handleProgress = (progress) => {
    setPlayed(progress.played);
    
    // Update progress every 10 seconds
    if (Math.floor(progress.playedSeconds) % 10 === 0) {
      progressMutation.mutate({
        enrollmentId,
        lessonId: lesson.id,
        watchTimeSeconds: Math.floor(progress.playedSeconds),
        completed: progress.played > 0.9, // Mark as completed at 90%
      });
    }
  };

  return (
    <div className="video-player">
      <ReactPlayer
        ref={playerRef}
        url={lesson.contentUrl}
        width="100%"
        height="400px"
        controls
        onProgress={handleProgress}
        onDuration={setDuration}
        config={{
          file: {
            attributes: {
              controlsList: 'nodownload',
            },
          },
        }}
      />
      
      <div className="progress-info">
        <div className="progress-bar">
          <div 
            className="progress-fill" 
            style={{ width: `${played * 100}%` }}
          />
        </div>
        <span>{Math.floor(played * 100)}% completed</span>
      </div>
    </div>
  );
};

export default VideoPlayer;
```

## Security Implementation Checklist

### Backend Security
- [ ] Input validation with express-validator
- [ ] Rate limiting on all endpoints
- [ ] CORS configuration
- [ ] Helmet.js for security headers
- [ ] JWT token expiration and refresh
- [ ] Password hashing with bcrypt
- [ ] File upload validation
- [ ] SQL injection prevention (Prisma ORM)

### Frontend Security
- [ ] XSS prevention with Content Security Policy
- [ ] Secure token storage
- [ ] Input sanitization
- [ ] Protected routes implementation
- [ ] HTTPS enforcement

### Payment Security
- [ ] Webhook signature verification
- [ ] No sensitive payment data storage
- [ ] PCI DSS compliance through Razorpay
- [ ] Secure payment flow implementation

## Deployment Strategy

### Development Environment
1. Local PostgreSQL database
2. Local Redis instance
3. Cloudinary for file storage
4. Razorpay test mode

### Production Environment
1. Railway/Render for backend hosting
2. Vercel for frontend hosting
3. Managed PostgreSQL (Railway/Supabase)
4. Redis Cloud for caching
5. Cloudinary for file storage
6. Razorpay production mode

## Estimated Timeline: 12 Weeks

**Weeks 1-2**: Backend foundation + Authentication
**Weeks 3-4**: Course management + Admin dashboard
**Weeks 5-6**: Payment integration + Enrollment system
**Weeks 7-8**: Student dashboard + Video player
**Weeks 9-10**: Email notifications + Advanced features
**Weeks 11-12**: Testing + Deployment + Optimization

This implementation plan provides a solid foundation for building a comprehensive e-learning platform while maintaining your existing website's design and functionality.
