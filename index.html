<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- Favicon -->
    <link rel="icon" href="/favicon.ico" sizes="any" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/svg+xml" href="/favicon-16x16.svg" sizes="16x16" />
    <link rel="icon" type="image/svg+xml" href="/favicon-32x32.svg" sizes="32x32" />
    <link rel="icon" type="image/svg+xml" href="/favicon-48x48.svg" sizes="48x48" />
    <link rel="icon" type="image/svg+xml" href="/favicon-192x192.svg" sizes="192x192" />
    <link rel="apple-touch-icon" href="/favicon-192x192.svg" />

    <!-- Web App Manifest - Disabled to prevent PWA installation prompts -->
    <!-- <link rel="manifest" href="/manifest.json" /> -->

    <!-- Primary Meta Tags -->
    <title>Hrishikesh Mohite | AI Strategist, Education & Tech Innovator</title>
    <meta name="title" content="Hrishikesh Mohite | AI Strategist, Education & Tech Innovator">
    <meta name="description" content="AI strategist transforming businesses with custom machine learning & AI in education solutions. Expert tech mentorship & Gemini Google innovations for measurable results." />
    <meta name="keywords" content="AI strategist, AI in education, business transformation, custom machine learning development, AI development, tech mentorship, full-stack developer, enterprise AI solutions, digital business transformation, AI consultant, generative AI, AI innovation, React development, Node.js development, TypeScript development, machine learning strategy, AI integration, EdTech AI, AI training, AI strategy consultant, intelligent automation, custom software development, AI solutions, data-driven AI, emerging tech mentor, AI-powered education, future of AI" />
    <meta name="author" content="Hrishikesh Mohite" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://hrishikeshmohite.com" />

    <!-- Page-specific meta tags will be set dynamically by JavaScript -->
    <script type="text/javascript">
      // Set page-specific meta tags based on URL path
      document.addEventListener('DOMContentLoaded', function() {
        const path = window.location.pathname;
        const baseUrl = 'https://hrishikeshmohite.com';

        // Function to update all meta tags for a page
        function updateMetaTags(title, description, url) {
          // Update basic meta tags
          document.title = title;
          document.querySelector('meta[name="title"]').setAttribute('content', title);
          document.querySelector('meta[name="description"]').setAttribute('content', description);

          // Update canonical URL
          document.querySelector('link[rel="canonical"]').setAttribute('href', url);

          // Update Open Graph tags
          document.querySelector('meta[property="og:title"]').setAttribute('content', title);
          document.querySelector('meta[property="og:description"]').setAttribute('content', description);
          document.querySelector('meta[property="og:url"]').setAttribute('content', url);

          // Update Twitter tags
          document.querySelector('meta[name="twitter:title"]').setAttribute('content', title);
          document.querySelector('meta[name="twitter:description"]').setAttribute('content', description);
          document.querySelector('meta[name="twitter:url"]').setAttribute('content', url);
        }

        // About page meta tags
        if (path === '/about') {
          updateMetaTags(
            'AI Strategist & Tech Innovator | Hrishikesh Mohite',
            'Meet Hrishikesh Mohite, AI strategist & tech innovator specializing in business transformation, AI in education, full-stack development & tech mentorship.',
            baseUrl + '/about'
          );
        }

        // Services page meta tags
        else if (path === '/services') {
          updateMetaTags(
            'AI Strategy & Full-Stack Development Services',
            'Expert AI in education, ChatGPT, Gemini Google & Microsoft Copilot solutions. Custom AI solutions, full-stack development & tech mentorship services.',
            baseUrl + '/services'
          );
        }

        // Portfolio page meta tags
        else if (path === '/portfolio') {
          updateMetaTags(
            'Portfolio | AI & Full-Stack Development Projects | Hrishikesh Mohite',
            'Explore Hrishikesh Mohite\'s portfolio of AI solutions, full-stack applications, and digital design projects. See how innovative technology solves real business challenges.',
            baseUrl + '/portfolio'
          );
        }

        // Books page meta tags
        else if (path === '/books') {
          updateMetaTags(
            'Books by Hrishikesh Mohite | Technology, Innovation & Leadership',
            'Discover books authored by Hrishikesh Mohite on technology, innovation, entrepreneurship, and leadership. Gain insights from his expertise and experience.',
            baseUrl + '/books'
          );
        }

        // Courses page meta tags
        else if (path === '/courses') {
          updateMetaTags(
            'Expert-Led Courses | AI, Full-Stack Development & Business Strategy',
            'Transform your skills with comprehensive courses in AI, full-stack development, business strategy, and design. Expert-led training with hands-on projects and industry certifications.',
            baseUrl + '/courses'
          );
        }

        // Contact page meta tags
        else if (path === '/contact') {
          updateMetaTags(
            'Contact Hrishikesh Mohite | AI Development & Tech Mentorship in India',
            'Get in touch with Hrishikesh Mohite for AI development, full-stack projects, and tech mentorship. Professional services available across India with remote collaboration options.',
            baseUrl + '/contact'
          );
        }

        // Privacy page meta tags
        else if (path === '/privacy') {
          updateMetaTags(
            'Privacy Policy | Hrishikesh Mohite',
            'Privacy policy for Hrishikesh Mohite\'s portfolio website. Learn how your data is collected, used, and protected when you visit hrishikeshmohite.com.',
            baseUrl + '/privacy'
          );
        }
      });
    </script>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://hrishikeshmohite.com" />
    <meta property="og:title" content="Hrishikesh Mohite | AI Strategist, Education & Tech Innovator" />
    <meta property="og:description" content="AI strategist transforming businesses with custom machine learning & AI in education solutions. Expert tech mentorship & Gemini Google innovations for measurable results." />
    <meta property="og:image" content="https://hrishikeshmohite.com/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:site_name" content="Hrishikesh Mohite" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://hrishikeshmohite.com" />
    <meta name="twitter:title" content="Hrishikesh Mohite | AI Strategist, Education & Tech Innovator" />
    <meta name="twitter:description" content="AI strategist transforming businesses with custom machine learning & AI in education solutions. Expert tech mentorship & Gemini Google innovations for measurable results." />
    <meta name="twitter:image" content="https://hrishikeshmohite.com/og-image.jpg" />
    <meta name="twitter:image:alt" content="Hrishikesh Mohite - Professional Portrait of AI Strategist and Full-Stack Developer from India" />
    <meta name="twitter:creator" content="@hrishikeshmohite" />

    <!-- Additional Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <meta name="theme-color" content="#0070f3" media="(prefers-color-scheme: light)" />
    <meta name="theme-color" content="#0050b3" media="(prefers-color-scheme: dark)" />

    <!-- Search Engine Verification Tags -->
    <meta name="google-site-verification" content="BXNdcI77VaN3A0VJb3YXimN-PWBktUJCG8Uoeaf7BDA" />
    <meta name="msvalidate.01" content="50D317703FD337701EB3E9794175CD7D" />

    <!-- Structured Data - Person -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "@id": "https://hrishikeshmohite.com/#person",
      "name": "Hrishikesh Mohite",
      "givenName": "Hrishikesh",
      "familyName": "Mohite",
      "url": "https://hrishikeshmohite.com",
      "image": "https://hrishikeshmohite.com/Hrishikesh-Mohite-professional-portrait.png",
      "jobTitle": "AI Strategist & Full-Stack Developer",
      "worksFor": {
        "@type": "Organization",
        "name": "Ajinkya Creatiion Pvt. Ltd."
      },
      "description": "Hrishikesh Mohite is an entrepreneur, AI strategist, full-stack developer, digital designer, and tech mentor specializing in AI development, web applications, and business strategy.",
      "sameAs": [
        "https://linkedin.com/in/hrishikeshmohite",
        "https://github.com/hrishikeshmohite",
        "https://behance.net/hrishikeshmohite"
      ],
      "knowsAbout": [
        "AI Development",
        "Full-Stack Development",
        "React",
        "Node.js",
        "TypeScript",
        "Kotlin",
        "Tech Mentorship",
        "Digital Design"
      ],
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "India"
      }
    }
    </script>

    <!-- Structured Data - SiteNavigationElement -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SiteNavigationElement",
      "name": [
        "Home",
        "About",
        "Services",
        "Portfolio",
        "Books",
        "Courses",
        "Contact"
      ],
      "url": [
        "https://hrishikeshmohite.com",
        "https://hrishikeshmohite.com/about",
        "https://hrishikeshmohite.com/services",
        "https://hrishikeshmohite.com/portfolio",
        "https://hrishikeshmohite.com/books",
        "https://hrishikeshmohite.com/courses",
        "https://hrishikeshmohite.com/contact"
      ]
    }
    </script>

    <!-- Structured Data - BreadcrumbList -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://hrishikeshmohite.com"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "About",
          "item": "https://hrishikeshmohite.com/about"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Services",
          "item": "https://hrishikeshmohite.com/services"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Portfolio",
          "item": "https://hrishikeshmohite.com/portfolio"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Books",
          "item": "https://hrishikeshmohite.com/books"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Courses",
          "item": "https://hrishikeshmohite.com/courses"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Contact",
          "item": "https://hrishikeshmohite.com/contact"
        }
      ]
    }
    </script>

    <!-- Structured Data - WebSite -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "@id": "https://hrishikeshmohite.com/#website",
      "url": "https://hrishikeshmohite.com",
      "name": "Hrishikesh Mohite | AI Strategist, Education & Tech Innovator",
      "description": "AI strategist transforming businesses with custom machine learning & AI in education solutions. Expert tech mentorship & Gemini Google innovations for measurable results.",
      "publisher": {
        "@id": "https://hrishikeshmohite.com/#person"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://hrishikeshmohite.com/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <!-- Structured Data - CreativeWork (Books) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "@id": "https://hrishikeshmohite.com/books#booklist",
      "name": "Books by Hrishikesh Mohite",
      "description": "Books authored by Hrishikesh Mohite on technology, innovation, entrepreneurship, and leadership.",
      "url": "https://hrishikeshmohite.com/books",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "item": {
            "@type": "Book",
            "name": "The AI Advantage: Transforming Business in the Digital Age",
            "author": {
              "@type": "Person",
              "@id": "https://hrishikeshmohite.com/#person"
            },
            "description": "A comprehensive guide to implementing AI solutions in business contexts, with practical strategies for digital transformation.",
            "genre": "Technology, Business Strategy",
            "inLanguage": "en"
          }
        },
        {
          "@type": "ListItem",
          "position": 2,
          "item": {
            "@type": "Book",
            "name": "Full-Stack Mastery: Modern Web Development Techniques",
            "author": {
              "@type": "Person",
              "@id": "https://hrishikeshmohite.com/#person"
            },
            "description": "A practical guide to full-stack development with modern frameworks and best practices for building scalable web applications.",
            "genre": "Technology, Programming",
            "inLanguage": "en"
          }
        },
        {
          "@type": "ListItem",
          "position": 3,
          "item": {
            "@type": "Book",
            "name": "Tech Leadership: Navigating Innovation in the Digital Era",
            "author": {
              "@type": "Person",
              "@id": "https://hrishikeshmohite.com/#person"
            },
            "description": "Insights on leadership in technology-driven organizations, with strategies for fostering innovation and managing digital transformation.",
            "genre": "Leadership, Technology Management",
            "inLanguage": "en"
          }
        }
      ]
    }
    </script>

    <!-- Structured Data - Professional Service -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "ProfessionalService",
      "@id": "https://hrishikeshmohite.com/#professionalservice",
      "name": "Hrishikesh Mohite - Professional Tech Services",
      "url": "https://hrishikeshmohite.com",
      "logo": "https://hrishikeshmohite.com/Hrishikesh-Mohite-professional-portrait.png",
      "image": "https://hrishikeshmohite.com/Hrishikesh-Mohite-professional-portrait.png",
      "description": "Professional tech services including AI development, full-stack development, tech mentorship, and business strategy consulting by Hrishikesh Mohite.",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "Global"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Tech Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "AI Development & Strategy",
              "description": "Custom AI solutions and strategic consulting to transform your business with cutting-edge technology."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Full-Stack Development",
              "description": "End-to-end web and mobile application development with modern technologies and best practices."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Tech Trainer & Mentor",
              "description": "Empowering aspiring developers, designers, and entrepreneurs with the knowledge and skills needed to excel in the digital landscape."
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Business Strategy",
              "description": "Strategic business consulting to help you navigate challenges and capitalize on opportunities."
            }
          }
        ]
      },
      "sameAs": [
        "https://linkedin.com/in/hrishikeshmohite",
        "https://github.com/hrishikeshmohite",
        "https://behance.net/hrishikeshmohite"
      ]
    }
    </script>

    <!-- Structured Data - FAQ Page -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What types of projects does Hrishikesh typically work on?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "I work on a diverse range of projects including AI implementation, web and mobile application development, business strategy consulting, and creative design. I particularly enjoy projects that combine multiple disciplines, such as developing AI-enhanced web applications or creating comprehensive digital transformation strategies."
          }
        },
        {
          "@type": "Question",
          "name": "How does the project process typically work?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "My process begins with a thorough discovery phase to understand your business needs and objectives. From there, I develop a tailored strategy and implementation plan. For development projects, I follow an agile methodology with regular check-ins and iterations. Throughout the process, I maintain clear communication and focus on delivering measurable value."
          }
        },
        {
          "@type": "Question",
          "name": "What makes Hrishikesh's approach different?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "My interdisciplinary background allows me to see projects from multiple perspectives - technical, design, business, and user experience. This holistic approach results in solutions that are not only technically sound but also aesthetically pleasing, business-aligned, and user-friendly."
          }
        },
        {
          "@type": "Question",
          "name": "What services does Hrishikesh offer?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "I offer a comprehensive range of services including AI Development & Strategy, Full-Stack App Development, Tech Trainer & Mentor services, Business & Branding Strategy, Graphic & Motion Design, Video Editing & Production, and Web & UI/UX Design. Each service can be tailored to your specific business needs and goals."
          }
        },
        {
          "@type": "Question",
          "name": "How can I contact Hrishikesh for a project?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "You can reach me through <NAME_EMAIL>, connect with me on LinkedIn, or use the contact form on my website. I typically respond to inquiries within 24-48 hours and am available for remote collaboration worldwide."
          }
        }
      ]
    }
    </script>

    <!-- Structured Data - Local Business -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "@id": "https://hrishikeshmohite.com/#localbusiness",
      "name": "Hrishikesh Mohite - AI Development & Tech Mentorship",
      "image": "https://hrishikeshmohite.com/Hrishikesh-Mohite-professional-portrait.png",
      "url": "https://hrishikeshmohite.com",
      "telephone": "+91-9876543210",
      "email": "<EMAIL>",
      "priceRange": "₹₹₹",
      "description": "AI strategist transforming businesses with custom machine learning & AI in education solutions. Expert tech mentorship & Gemini Google innovations for measurable results.",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "Global"
      },

      "openingHoursSpecification": {
        "@type": "OpeningHoursSpecification",
        "dayOfWeek": [
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday"
        ],
        "opens": "09:00",
        "closes": "18:00"
      },
      "sameAs": [
        "https://linkedin.com/in/hrishikeshmohite",
        "https://github.com/hrishikeshmohite",
        "https://behance.net/hrishikeshmohite",
        "https://www.justdial.com/hrishikeshmohite",
        "https://www.indiamart.com/hrishikeshmohite",
        "https://www.sulekha.com/hrishikeshmohite",
        "https://www.urbanpro.com/hrishikeshmohite"
      ],
      "founder": {
        "@id": "https://hrishikeshmohite.com/#person"
      },
      "knowsAbout": [
        "AI Development",
        "Full-Stack Development",
        "Tech Mentorship",
        "Digital Design",
        "Business Strategy"
      ],
      "areaServed": [
        {
          "@type": "Place",
          "name": "Global (Remote)"
        }
      ],
      "makesOffer": [
        {
          "@type": "Offer",
          "name": "AI Development & Strategy",
          "description": "Custom AI solutions and strategic consulting to transform your business."
        },
        {
          "@type": "Offer",
          "name": "Full-Stack Development",
          "description": "End-to-end web and mobile application development."
        },
        {
          "@type": "Offer",
          "name": "Tech Training & Mentorship",
          "description": "Personalized tech training and mentorship programs."
        }
      ]
    }
    </script>

    <!-- Resource Hints -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="dns-prefetch" href="https://www.google-analytics.com">
    <link rel="dns-prefetch" href="https://www.googletagmanager.com">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="/src/assets/images/hero/optimized/Hrishikesh-Mohite-professional-portrait.png" as="image" type="image/png" fetchPriority="high">

    <!-- Fonts with display=swap for better performance -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet" media="print" onload="this.media='all'">
    <!-- Fallback for browsers that don't support JS font loading -->
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    </noscript>
  </head>
  <body>


    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
