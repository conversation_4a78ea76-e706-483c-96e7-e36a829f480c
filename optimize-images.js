// This script optimizes images while preserving color fidelity
// Run with: node optimize-images.js

/* eslint-env node */
/* eslint-disable no-undef */
/* eslint-disable no-unused-vars */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  // Directories to process
  directories: [
    'src/assets/images',
    'public'
  ],
  // File extensions to process
  extensions: ['.jpg', '.jpeg', '.png', '.webp'],
  // Special handling for profile images to preserve color fidelity
  profileImages: [
    'Hrishikesh-Mohite-professional-portrait.png',
    'Hrishikesh-Mohite-profile.jpg'
  ],
  // Special handling for large project images
  largeProjectImages: [
    'Hrishikesh-Mohite-predictive-analytics-dashboard.png',
    'Hrishikesh-Mohite-nlp-api.png',
    '<PERSON>rishikesh-Mohite-Neural-Network-Playground.png'
  ],

  // Additional problematic images that need special handling
  problematicImages: [
    'Hrishikesh-Mohite-mobile-app-ui-design.png',
    'Hrishikesh-Mohite-CareerCraft.png',
    'Hrishikesh-Mohite-product-explainer-video.jpg',
    'Hrishikesh-Mohite-control-pride-book.jpg',
    'Hrishikesh-Mohite-symphony-of-stars-book.jpg',
    'Hrishikesh-Mohite-time-to-show-book.jpg',
    'Hrishikesh-Mohite-love-through-adversity-book.jpg',
    'Hrishikesh-Mohite-entrepreneur-mindset-book.jpg',
    'Hrishikesh-Mohite-3d-product-visualization.jpg',
    'Hrishikesh-Mohite-corporate-brand-video.webp'
  ],
  // Output directory for optimized images (relative to each source directory)
  outputDir: 'optimized',
  // Quality settings
  quality: {
    default: 90, // Increased from 85 to better preserve color fidelity
    profile: 95, // Higher quality for profile images to preserve color fidelity
    project: 92  // High quality for project images to preserve colors
  },
  // Whether to create WebP versions
  createWebP: true,
  // Whether to preserve original files
  preserveOriginals: true
};

// Import Sharp
import sharp from 'sharp';

// Function to optimize an image
async function optimizeImage(filePath) {
  const fileName = path.basename(filePath);
  const directory = path.dirname(filePath);
  const extension = path.extname(filePath).toLowerCase();
  const nameWithoutExt = path.basename(filePath, extension);

  // Create output directory if it doesn't exist
  const outputDirectory = path.join(directory, config.outputDir);
  if (!fs.existsSync(outputDirectory)) {
    fs.mkdirSync(outputDirectory, { recursive: true });
  }

  // Determine if this is a special image that needs special handling
  const isProfileImage = config.profileImages.some(profile => fileName.includes(profile));
  const isLargeProjectImage = config.largeProjectImages.some(project => fileName.includes(project));
  const isProblematicImage = config.problematicImages.some(img => fileName.includes(img));

  // Set quality based on image type
  let quality;
  if (isProfileImage) {
    quality = config.quality.profile;
  } else if (isLargeProjectImage || isProblematicImage) {
    quality = config.quality.project; // Use high quality for both large and problematic images
  } else {
    quality = config.quality.default;
  }

  console.log(`Processing ${filePath} (${isProfileImage ? 'Profile Image' : isLargeProjectImage ? 'Large Project Image' : isProblematicImage ? 'Problematic Image' : 'Standard Image'})`);

  try {
    // Create a sharp instance with the input file
    let image = sharp(filePath);

    // Get image metadata
    const metadata = await image.metadata();

    // For all images, preserve color profiles and metadata
    image = image.withMetadata({
      // Preserve all metadata including color profiles
      preserveMetadata: ['icc', 'exif', 'xmp']
    });

    // Special handling for profile images to preserve color fidelity
    if (isProfileImage) {
      // Handle different output formats based on input format
      if (extension === '.jpg' || extension === '.jpeg') {
        image = image.jpeg({
          quality,
          mozjpeg: true,
          trellisQuantisation: true,
          overshootDeringing: true,
          optimizeScans: true,
          quantisationTable: 3
        });
      } else if (extension === '.png') {
        image = image.png({
          quality: quality, // Use integer value (0-100) for quality
          compressionLevel: 6, // Balanced compression
          adaptiveFiltering: true,
          palette: false, // Don't use palette to preserve full color range
          colors: 256 // Maximum colors per channel
        });
      }
    }
    // Special handling for large project images
    else if (isLargeProjectImage) {
      // For large project images, we'll resize them to reduce file size
      // while maintaining reasonable quality
      const width = metadata.width;

      // Resize to a maximum width of 1200px while maintaining aspect ratio
      const maxWidth = 1200;
      const resizeOptions = width > maxWidth ? { width: maxWidth } : {};

      image = image.resize(resizeOptions);

      // Apply format-specific optimizations with higher quality to preserve colors
      if (extension === '.jpg' || extension === '.jpeg') {
        image = image.jpeg({
          quality,
          mozjpeg: true,
          trellisQuantisation: true,
          overshootDeringing: true,
          optimizeScans: true,
          quantisationTable: 3
        });
      } else if (extension === '.png') {
        image = image.png({
          quality: quality,
          compressionLevel: 7, // Reduced from 8 to preserve color fidelity
          adaptiveFiltering: true,
          palette: false, // Don't use palette to preserve full color range
          colors: 256 // Maximum colors per channel
        });
      }
    }
    // Standard optimization for regular images
    else {
      // Standard optimization with color profile preservation
      if (extension === '.jpg' || extension === '.jpeg') {
        image = image.jpeg({
          quality,
          mozjpeg: true,
          trellisQuantisation: true,
          overshootDeringing: true,
          optimizeScans: true,
          quantisationTable: 3
        });
      } else if (extension === '.png') {
        image = image.png({
          quality: quality,
          compressionLevel: 7, // Reduced from 9 to preserve color fidelity
          adaptiveFiltering: true,
          palette: false, // Don't use palette to preserve full color range
          colors: 256 // Maximum colors per channel
        });
      }
    }

    // Output optimized image
    const outputPath = path.join(outputDirectory, fileName);
    await image.toFile(outputPath);
    console.log(`Optimized image saved to ${outputPath}`);

    // Create WebP version if configured
    if (config.createWebP) {
      const webpOutputPath = path.join(outputDirectory, `${nameWithoutExt}.webp`);

      // Use appropriate WebP settings based on image type
      let webpOptions = {
        quality: quality, // Use same quality as original format
        alphaQuality: 100, // Preserve alpha channel quality
        smartSubsample: true, // Preserve color detail in chroma channels
        effort: 6 // Higher effort for better quality (0-6)
      };

      // For profile images, use lossless to preserve exact colors
      if (isProfileImage) {
        webpOptions.lossless = true;
        webpOptions.nearLossless = true;
      }

      await image.webp(webpOptions).toFile(webpOutputPath);
      console.log(`WebP version saved to ${webpOutputPath}`);
    }

    return {
      success: true,
      filePath,
      outputPath
    };
  } catch (error) {
    console.error(`Error optimizing ${filePath}:`, error);
    return {
      success: false,
      filePath,
      error
    };
  }
}

// Function to process a directory
async function processDirectory(directory) {
  console.log(`Processing directory: ${directory}`);

  // Get all files in the directory
  const files = fs.readdirSync(directory);

  // Process each file
  for (const file of files) {
    const filePath = path.join(directory, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      // Skip the output directory
      if (file === config.outputDir) {
        continue;
      }

      // Recursively process subdirectories
      await processDirectory(filePath);
    } else if (stats.isFile()) {
      // Check if the file has a supported extension
      const extension = path.extname(file).toLowerCase();
      if (config.extensions.includes(extension)) {
        await optimizeImage(filePath);
      }
    }
  }
}

// Main function
async function main() {
  console.log('Starting image optimization...');

  // Check command line arguments
  const profileOnly = process.argv.includes('--profile-only');
  const projectOnly = process.argv.includes('--project-only');
  const problematicOnly = process.argv.includes('--problematic-only');

  if (profileOnly) {
    console.log('Processing profile images only...');

    // Find and process only profile images
    for (const directory of config.directories) {
      const processProfilesOnly = async (dir) => {
        const files = fs.readdirSync(dir);

        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = fs.statSync(filePath);

          if (stats.isDirectory()) {
            if (file !== config.outputDir) {
              await processProfilesOnly(filePath);
            }
          } else if (stats.isFile()) {
            const isProfileImage = config.profileImages.some(profile => file.includes(profile));
            if (isProfileImage) {
              await optimizeImage(filePath);
            }
          }
        }
      };

      await processProfilesOnly(directory);
    }
  } else if (projectOnly) {
    console.log('Processing large project images only...');

    // Find and process only large project images
    for (const directory of config.directories) {
      const processProjectsOnly = async (dir) => {
        const files = fs.readdirSync(dir);

        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = fs.statSync(filePath);

          if (stats.isDirectory()) {
            if (file !== config.outputDir) {
              await processProjectsOnly(filePath);
            }
          } else if (stats.isFile()) {
            const isLargeProjectImage = config.largeProjectImages.some(project => file.includes(project));
            if (isLargeProjectImage) {
              await optimizeImage(filePath);
            }
          }
        }
      };

      await processProjectsOnly(directory);
    }
  } else if (problematicOnly) {
    console.log('Processing problematic images only...');

    // Find and process only problematic images
    for (const directory of config.directories) {
      const processProblematicOnly = async (dir) => {
        const files = fs.readdirSync(dir);

        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = fs.statSync(filePath);

          if (stats.isDirectory()) {
            if (file !== config.outputDir) {
              await processProblematicOnly(filePath);
            }
          } else if (stats.isFile()) {
            const isProblematicImage = config.problematicImages.some(img => file.includes(img));
            if (isProblematicImage) {
              await optimizeImage(filePath);
            }
          }
        }
      };

      await processProblematicOnly(directory);
    }
  } else {
    // Process all images in each configured directory
    for (const directory of config.directories) {
      await processDirectory(directory);
    }
  }

  console.log('Image optimization complete!');
}

// Run the script
main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
