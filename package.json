{"name": "hris<PERSON><PERSON><PERSON>-mohite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "node build-production.cjs", "optimize:images": "node optimize-images.js", "optimize:profile": "node optimize-images.js --profile-only", "optimize:projects": "node optimize-images.js --project-only", "optimize:problematic": "node optimize-images.js --problematic-only", "create:favicon": "node create-favicon-ico.js", "analyze": "vite build --mode analyze", "lint": "eslint .", "preview": "vite preview", "lighthouse": "lighthouse http://localhost:4174 --view --output-path=./lighthouse-report.html"}, "dependencies": {"@emailjs/browser": "^4.4.1", "framer-motion": "^12.12.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "styled-components": "^6.1.18"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "compression-webpack-plugin": "^11.1.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "lighthouse": "^12.6.1", "rollup-plugin-visualizer": "^5.14.0", "sharp": "^0.34.1", "terser": "^5.39.2", "to-ico": "^1.0.1", "vite": "^6.3.5", "vite-plugin-imagemin": "^0.1.0", "workbox-cli": "^7.3.0"}}