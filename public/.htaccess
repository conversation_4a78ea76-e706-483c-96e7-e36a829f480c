# Enable rewriting
RewriteEngine On

# Redirect www to non-www (301 permanent redirect)
RewriteCond %{HTTP_HOST} ^www\.hrishikeshmohite\.com$ [NC]
RewriteRule ^(.*)$ https://hrishikeshmohite.com/$1 [R=301,L]

# If the request is not for a file or directory
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Rewrite all requests to the index.html file
RewriteRule ^(.*)$ index.html [QSA,L]

# Enable GZIP compression for better performance
<IfModule mod_deflate.c>
  # Compress HTML, CSS, JavaScript, Text, XML and fonts
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
  AddOutputFilterByType DEFLATE application/x-font
  AddOutputFilterByType DEFLATE application/x-font-opentype
  AddOutputFilterByType DEFLATE application/x-font-otf
  AddOutputFilterByType DEFLATE application/x-font-truetype
  AddOutputFilterByType DEFLATE application/x-font-ttf
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE font/opentype
  AddOutputFilterByType DEFLATE font/otf
  AddOutputFilterByType DEFLATE font/ttf
  AddOutputFilterByType DEFLATE image/svg+xml
  AddOutputFilterByType DEFLATE image/x-icon
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/javascript
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Set browser caching with comprehensive expires headers
<IfModule mod_expires.c>
  ExpiresActive On

  # Images - 1 year cache
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/jpg "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/avif "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"
  ExpiresByType image/vnd.microsoft.icon "access plus 1 year"

  # Fonts - 1 year cache
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType font/ttf "access plus 1 year"
  ExpiresByType font/otf "access plus 1 year"
  ExpiresByType font/eot "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"
  ExpiresByType application/vnd.ms-fontobject "access plus 1 year"
  ExpiresByType application/x-font-ttf "access plus 1 year"
  ExpiresByType application/x-font-otf "access plus 1 year"

  # Video and Audio - 1 year cache
  ExpiresByType video/mp4 "access plus 1 year"
  ExpiresByType video/mpeg "access plus 1 year"
  ExpiresByType video/webm "access plus 1 year"
  ExpiresByType video/ogg "access plus 1 year"
  ExpiresByType audio/mp3 "access plus 1 year"
  ExpiresByType audio/mpeg "access plus 1 year"
  ExpiresByType audio/ogg "access plus 1 year"
  ExpiresByType audio/wav "access plus 1 year"

  # CSS and JavaScript - 1 month cache
  ExpiresByType text/css "access plus 1 month"
  ExpiresByType text/javascript "access plus 1 month"
  ExpiresByType application/javascript "access plus 1 month"
  ExpiresByType application/x-javascript "access plus 1 month"

  # Documents and Archives - 1 month cache
  ExpiresByType application/pdf "access plus 1 month"
  ExpiresByType application/zip "access plus 1 month"
  ExpiresByType application/x-zip-compressed "access plus 1 month"
  ExpiresByType application/msword "access plus 1 month"
  ExpiresByType application/vnd.openxmlformats-officedocument.wordprocessingml.document "access plus 1 month"
  ExpiresByType application/vnd.ms-excel "access plus 1 month"
  ExpiresByType application/vnd.openxmlformats-officedocument.spreadsheetml.sheet "access plus 1 month"

  # Manifest and XML files - 1 week cache
  ExpiresByType application/manifest+json "access plus 1 week"
  ExpiresByType application/xml "access plus 1 week"
  ExpiresByType text/xml "access plus 1 week"

  # Default fallback - 1 month cache
  ExpiresDefault "access plus 1 month"
</IfModule>

# Set security headers
# Note: The following XML-like tags are Apache directives, not PowerShell redirection operators
<IfModule mod_headers.c>
  # Protect against XSS attacks
  Header set X-XSS-Protection "1; mode=block"

  # Prevent MIME-type sniffing
  Header set X-Content-Type-Options "nosniff"

  # Prevent clickjacking
  Header set X-Frame-Options "SAMEORIGIN"

  # Enable HSTS (HTTP Strict Transport Security)
  Header set Strict-Transport-Security "max-age=31536000; includeSubDomains"

  # Disable caching for sensitive files
  <FilesMatch "\.(html|htm|php)$">
    Header set Cache-Control "private, no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
  </FilesMatch>

  # Enable caching for static assets with versioning
  # Long-term caching for images, fonts, and media (1 year = 31536000 seconds)
  <FilesMatch "\.(jpg|jpeg|png|gif|webp|avif|svg|ico|woff|woff2|ttf|otf|eot|mp4|webm|ogg|mp3|wav)$">
    Header set Cache-Control "public, max-age=31536000, immutable"
    Header set Vary "Accept-Encoding"
  </FilesMatch>

  # Medium-term caching for CSS and JavaScript (1 month = 2592000 seconds)
  <FilesMatch "\.(css|js)$">
    Header set Cache-Control "public, max-age=2592000"
    Header set Vary "Accept-Encoding"
  </FilesMatch>

  # Short-term caching for documents and archives (1 month = 2592000 seconds)
  <FilesMatch "\.(pdf|zip|doc|docx|xls|xlsx)$">
    Header set Cache-Control "public, max-age=2592000"
  </FilesMatch>
</IfModule>
