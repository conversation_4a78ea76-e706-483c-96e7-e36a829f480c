import { Routes, Route, useLocation } from 'react-router-dom';
import { lazy, Suspense, useEffect } from 'react';
import Layout from './components/layout/Layout';
import CookieConsent from './components/cookie/CookieConsent';
import { initializeAnalytics, trackPageView } from './utils/analytics';
import { hasConsentForCategory, COOKIE_CATEGORIES } from './utils/cookieManager';

// Lazy load pages for better performance
const Home = lazy(() => import('./pages/Home'));
const About = lazy(() => import('./pages/About'));
const Services = lazy(() => import('./pages/Services'));
const Portfolio = lazy(() => import('./pages/Portfolio'));
const Books = lazy(() => import('./pages/Books'));
const Courses = lazy(() => import('./pages/Courses'));
const Contact = lazy(() => import('./pages/Contact'));
// Import Privacy and CookieSettings directly instead of lazy loading
import PrivacySimple from './pages/PrivacySimple';
import CookieSettings from './pages/CookieSettings';
const NotFound = lazy(() => import('./pages/NotFound'));

// Loading component
const PageLoader = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh'
  }}>
    <div style={{
      width: '50px',
      height: '50px',
      border: '5px solid #f3f3f3',
      borderTop: '5px solid #0070f3',
      borderRadius: '50%',
      animation: 'spin 1s linear infinite'
    }} />
    <style>{`
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `}</style>
  </div>
);

function App() {
  const location = useLocation();

  // Initialize analytics if user has consented
  useEffect(() => {
    if (hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
      initializeAnalytics();
    }
  }, []);

  // Track page views when location changes
  useEffect(() => {
    // Get page title
    const pageTitle = document.title;

    // Track page view if user has consented to analytics
    if (hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
      trackPageView(location.pathname, pageTitle);
    }
  }, [location]);

  return (
    <>
      <Routes>
        <Route path="/" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Home />
            </Suspense>
          </Layout>
        } />
        <Route path="/about" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <About />
            </Suspense>
          </Layout>
        } />
        <Route path="/services" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Services />
            </Suspense>
          </Layout>
        } />
        <Route path="/portfolio" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Portfolio />
            </Suspense>
          </Layout>
        } />
        <Route path="/books" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Books />
            </Suspense>
          </Layout>
        } />
        <Route path="/courses" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Courses />
            </Suspense>
          </Layout>
        } />
        <Route path="/contact" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <Contact />
            </Suspense>
          </Layout>
        } />
        <Route path="/privacy" element={
          <Layout>
            <PrivacySimple />
          </Layout>
        } />
        <Route path="/cookie-settings" element={
          <Layout>
            <CookieSettings />
          </Layout>
        } />
        <Route path="*" element={
          <Layout>
            <Suspense fallback={<PageLoader />}>
              <NotFound />
            </Suspense>
          </Layout>
        } />
      </Routes>

      {/* Cookie Consent Banner */}
      <CookieConsent />
    </>
  );
}

export default App;
