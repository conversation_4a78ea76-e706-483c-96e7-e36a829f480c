import { useRef, useEffect } from 'react';
import { FiSend } from 'react-icons/fi';
import { useChatbot } from '../../context/useChatbot';
import styles from './ChatInput.module.css';

/**
 * Component for the chat input field and send icon button
 */
const ChatInput = () => {
  const { inputValue, handleInputChange, handleSubmit, isLoading } = useChatbot();
  const inputRef = useRef(null);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Handle Enter key press
  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  return (
    <form className={styles.inputContainer} onSubmit={handleSubmit}>
      <input
        ref={inputRef}
        type="text"
        className={styles.input}
        placeholder="Ask about services, portfolio, or contact info..."
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        disabled={isLoading}
      />
      <button
        type="submit"
        className={styles.sendButton}
        disabled={isLoading || !inputValue.trim()}
        aria-label="Send message"
      >
        <FiSend className={styles.sendIcon} size={18} />
      </button>
    </form>
  );
};

export default ChatInput;
