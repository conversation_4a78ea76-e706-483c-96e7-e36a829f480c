.inputContainer {
  display: flex;
  padding: var(--spacing-md);
  border-top: 1px solid var(--color-border);
  background-color: var(--color-background);
}

.input {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--fs-sm);
  background-color: var(--color-background);
  color: var(--color-text);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.input:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.input::placeholder {
  color: var(--color-text-lighter);
}

.input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.sendButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-left: var(--spacing-sm);
  border-radius: var(--radius-full);
  background-color: var(--color-primary);
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

.sendButton:hover:not(:disabled) {
  background-color: var(--color-primary-light);
  transform: translateY(-2px);
}

.sendButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.sendIcon {
  color: white;
  /* Ensure icon is centered and visible */
  position: relative;
  z-index: 2;
}

/* Responsive styles */
@media (max-width: 768px) {
  .inputContainer {
    padding: var(--spacing-sm);
  }

  .input {
    padding: var(--spacing-sm);
    font-size: var(--fs-xs);
  }

  .sendButton {
    width: 36px;
    height: 36px;
  }
}
