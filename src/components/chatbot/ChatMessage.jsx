import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FiUser, FiCpu } from 'react-icons/fi';
import styles from './ChatMessage.module.css';

/**
 * Component to render a single chat message
 */
const ChatMessage = ({ message }) => {
  const [formattedText, setFormattedText] = useState('');
  
  // Format message text with markdown-like syntax
  useEffect(() => {
    if (!message.text) return;
    
    let text = message.text;
    
    // Convert markdown-style links to actual links
    text = text.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, linkText, url) => {
      // Check if it's an internal link
      if (url.startsWith('/')) {
        return `<a href="${url}" class="${styles.internalLink}" data-internal="true">${linkText}</a>`;
      }
      // External link
      return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="${styles.externalLink}">${linkText}</a>`;
    });
    
    // Convert bold text
    text = text.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    
    // Convert bullet points
    text = text.replace(/^• (.+)$/gm, '<li>$1</li>');
    text = text.replace(/<li>(.+)<\/li>/g, '<ul><li>$1</li></ul>');
    
    // Combine consecutive ul elements
    text = text.replace(/<\/ul>\s*<ul>/g, '');
    
    // Convert line breaks to <br> tags
    text = text.replace(/\n/g, '<br>');
    
    setFormattedText(text);
  }, [message.text]);
  
  // Handle click on internal links
  const handleLinkClick = (e) => {
    const target = e.target;
    if (target.tagName === 'A' && target.dataset.internal === 'true') {
      e.preventDefault();
      window.location.href = target.getAttribute('href');
    }
  };
  
  return (
    <div className={`${styles.messageContainer} ${message.type === 'user' ? styles.userMessage : styles.botMessage}`}>
      <div className={styles.messageAvatar}>
        {message.type === 'user' ? (
          <div className={styles.userAvatar}>
            <FiUser size={16} />
          </div>
        ) : (
          <div className={styles.botAvatar}>
            <FiCpu size={16} />
          </div>
        )}
      </div>
      <div className={styles.messageContent}>
        <div 
          className={`${styles.messageText} ${message.isError ? styles.errorMessage : ''}`}
          dangerouslySetInnerHTML={{ __html: formattedText }}
          onClick={handleLinkClick}
        />
        <div className={styles.messageTime}>
          {formatTime(message.timestamp)}
        </div>
      </div>
    </div>
  );
};

/**
 * Format timestamp to readable time
 */
const formatTime = (timestamp) => {
  if (!timestamp) return '';
  
  const date = new Date(timestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

export default ChatMessage;
