.messageContainer {
  display: flex;
  margin-bottom: var(--spacing-md);
  animation: fadeIn 0.3s ease-out;
}

.userMessage {
  justify-content: flex-end;
}

.botMessage {
  justify-content: flex-start;
}

.messageAvatar {
  display: flex;
  align-items: flex-start;
  margin-top: 5px;
}

.userMessage .messageAvatar {
  order: 2;
  margin-left: var(--spacing-sm);
}

.botMessage .messageAvatar {
  margin-right: var(--spacing-sm);
}

.userAvatar, .botAvatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.userAvatar {
  background-color: var(--color-secondary);
  color: white;
}

.botAvatar {
  background-color: var(--color-primary);
  color: white;
}

.messageContent {
  max-width: 80%;
}

.messageText {
  padding: var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--fs-sm);
  line-height: 1.5;
  word-break: break-word;
}

.userMessage .messageText {
  background-color: var(--color-secondary);
  color: white;
  border-top-right-radius: 4px;
}

.botMessage .messageText {
  background-color: var(--color-background-alt);
  color: var(--color-text);
  border-top-left-radius: 4px;
}

.errorMessage {
  background-color: rgba(var(--color-error), 0.1) !important;
  color: var(--color-error) !important;
  border: 1px solid var(--color-error);
}

.messageTime {
  font-size: var(--fs-xs);
  color: var(--color-text-lighter);
  margin-top: 4px;
  text-align: right;
}

.botMessage .messageTime {
  text-align: left;
}

/* Link styles */
.internalLink, .externalLink {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--fw-medium);
  transition: color var(--transition-fast);
}

.botMessage .internalLink, .botMessage .externalLink {
  color: var(--color-primary);
}

.userMessage .internalLink, .userMessage .externalLink {
  color: white;
  text-decoration: underline;
}

.internalLink:hover, .externalLink:hover {
  text-decoration: underline;
}

/* Styling for formatted content */
.messageText strong {
  font-weight: var(--fw-bold);
}

.messageText ul {
  margin: var(--spacing-sm) 0;
  padding-left: var(--spacing-lg);
}

.messageText li {
  margin-bottom: var(--spacing-xs);
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .messageContent {
    max-width: 85%;
  }
  
  .messageText {
    padding: var(--spacing-sm);
    font-size: var(--fs-xs);
  }
  
  .userAvatar, .botAvatar {
    width: 25px;
    height: 25px;
  }
}

@media (max-width: 480px) {
  .messageContent {
    max-width: 90%;
  }
}
