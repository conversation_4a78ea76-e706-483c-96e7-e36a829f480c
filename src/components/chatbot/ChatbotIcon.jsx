import { useState, useEffect } from 'react';
import { FiMessageCircle, FiX } from 'react-icons/fi';
import { useChatbot } from '../../context/useChatbot';
import { useTheme } from '../../hooks/useTheme';
import styles from './ChatbotIcon.module.css';

/**
 * Floating chat icon component that toggles the chatbot visibility
 */
const ChatbotIcon = () => {
  const { isOpen, toggleChatbot } = useChatbot();
  const { theme } = useTheme();
  const [showTooltip, setShowTooltip] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);

  // Add initial attention animation after a delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setHasAnimated(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div
      className={`${styles.iconContainer} ${isOpen ? styles.open : ''} ${theme === 'dark' ? styles.dark : styles.light}`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      <button
        className={`${styles.iconButton} ${hasAnimated ? styles.animated : ''}`}
        onClick={toggleChatbot}
        aria-label={isOpen ? "Close chat" : "Open chat"}
      >
        {isOpen ? <FiX size={24} /> : <FiMessageCircle size={24} />}
      </button>

      {showTooltip && !isOpen && (
        <div className={styles.tooltip}>
          Ask me anything
        </div>
      )}
    </div>
  );
};

export default ChatbotIcon;
