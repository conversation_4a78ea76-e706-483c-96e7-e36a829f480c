.iconContainer {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: var(--z-fixed);
}

.iconButton {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.iconButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.iconButton:hover::before {
  opacity: 1;
}

.iconButton:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(var(--color-primary-rgb), 0.4);
}

.open .iconButton {
  background-color: var(--color-text-light);
  transform: rotate(90deg);
}

.open .iconButton:hover {
  background-color: var(--color-text);
  transform: rotate(90deg) translateY(-5px);
}

/* Tooltip */
.tooltip {
  position: absolute;
  bottom: 70px;
  right: 0;
  background-color: var(--color-background);
  color: var(--color-text);
  padding: 8px 12px;
  border-radius: var(--radius-md);
  font-size: var(--fs-sm);
  box-shadow: var(--shadow-md);
  white-space: nowrap;
  pointer-events: none;
  animation: fadeIn 0.3s ease-out;
  border: 1px solid var(--color-border);
}

.tooltip::after {
  content: '';
  position: absolute;
  bottom: -5px;
  right: 25px;
  width: 10px;
  height: 10px;
  background-color: var(--color-background);
  transform: rotate(45deg);
  border-right: 1px solid var(--color-border);
  border-bottom: 1px solid var(--color-border);
}

/* Animation for initial attention */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(var(--color-primary-rgb), 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animated {
  animation: pulse 1.5s ease-in-out;
}

/* Theme-specific styles */
.dark .iconButton {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

.dark .iconButton:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
}

/* Responsive styles */
@media (max-width: 768px) {
  .iconButton {
    width: 50px;
    height: 50px;
  }
  
  .iconContainer {
    bottom: 15px;
    right: 15px;
  }
}

@media (max-width: 480px) {
  .iconButton {
    width: 45px;
    height: 45px;
  }
}
