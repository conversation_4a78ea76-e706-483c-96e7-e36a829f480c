import { useRef, useEffect } from 'react';
import { useChatbot } from '../../context/useChatbot';
import { useTheme } from '../../hooks/useTheme';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import styles from './ChatbotWindow.module.css';

/**
 * Component for the chatbot window that displays messages and input
 */
const ChatbotWindow = () => {
  const { isOpen, messages, isLoading } = useChatbot();
  const { theme } = useTheme();
  const messagesEndRef = useRef(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Don't render if chatbot is closed
  if (!isOpen) return null;

  return (
    <div className={`${styles.chatWindow} ${theme === 'dark' ? styles.dark : styles.light}`}>
      <div className={styles.chatHeader}>
        <div className={styles.chatTitle}>
          <span className={styles.botName}><PERSON><PERSON><PERSON><PERSON><PERSON>'s Assistant</span>
          <span className={styles.botStatus}>
            {isLoading ? 'Thinking...' : 'Online'}
          </span>
        </div>
      </div>

      <div className={styles.chatMessages}>
        {messages.map((message) => (
          <ChatMessage key={message.id} message={message} />
        ))}

        {isLoading && (
          <div className={styles.loadingIndicator}>
            <div className={styles.typingDot}></div>
            <div className={styles.typingDot}></div>
            <div className={styles.typingDot}></div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <ChatInput />
    </div>
  );
};

export default ChatbotWindow;
