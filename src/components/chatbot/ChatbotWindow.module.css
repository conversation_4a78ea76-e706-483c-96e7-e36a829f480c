.chatWindow {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 350px;
  height: 500px;
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: var(--z-fixed);
  animation: slideIn 0.3s ease-out;
  border: 1px solid var(--color-border);
}

.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background-color: var(--color-primary);
  color: white;
}

.chatTitle {
  display: flex;
  flex-direction: column;
}

.botName {
  font-weight: var(--fw-semibold);
  font-size: var(--fs-sm);
}

.botStatus {
  font-size: var(--fs-xs);
  opacity: 0.8;
}

.chatActions {
  display: flex;
  gap: var(--spacing-xs);
}

.actionButton {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-full);
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.actionButton:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(0);
}

.chatMessages {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  background-color: var(--color-background);
}

/* Loading indicator */
.loadingIndicator {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: var(--spacing-md) 0;
  padding-left: var(--spacing-md);
}

.typingDot {
  width: 8px;
  height: 8px;
  background-color: var(--color-primary);
  border-radius: 50%;
  animation: typingAnimation 1.5s infinite ease-in-out;
}

.typingDot:nth-child(1) {
  animation-delay: 0s;
}

.typingDot:nth-child(2) {
  animation-delay: 0.3s;
}

.typingDot:nth-child(3) {
  animation-delay: 0.6s;
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typingAnimation {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

/* Theme-specific styles */
.dark {
  border-color: var(--color-border);
}

.light {
  border-color: var(--color-border-light);
}

/* Scrollbar styles */
.chatMessages::-webkit-scrollbar {
  width: 6px;
}

.chatMessages::-webkit-scrollbar-track {
  background: transparent;
}

.chatMessages::-webkit-scrollbar-thumb {
  background-color: var(--color-border);
  border-radius: var(--radius-full);
}

.chatMessages::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-text-lighter);
}

/* Responsive styles */
@media (max-width: 768px) {
  .chatWindow {
    width: calc(100% - 40px);
    height: 450px;
    bottom: 80px;
  }
}

@media (max-width: 480px) {
  .chatWindow {
    width: calc(100% - 30px);
    height: 400px;
    right: 15px;
    bottom: 75px;
  }
  
  .chatHeader {
    padding: var(--spacing-sm);
  }
  
  .chatMessages {
    padding: var(--spacing-sm);
  }
}
