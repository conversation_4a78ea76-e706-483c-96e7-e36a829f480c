import { useState, useEffect } from 'react';
import {
  getConsentData,
  saveFullConsent,
  COOKIE_CATEGORIES
} from '../../utils/cookieManager';
import { updateAnalyticsConsent } from '../../utils/analytics';
import styles from './CookieConsent.module.css';

/**
 * Cookie Consent Banner Component
 *
 * Displays a cookie consent banner that allows users to manage their cookie preferences.
 * This component is designed to be compatible with Chrome's third-party cookie phase-out.
 */
const CookieConsent = () => {
  // State for showing the banner
  const [showBanner, setShowBanner] = useState(false);

  // State for showing detailed preferences
  const [showPreferences, setShowPreferences] = useState(false);

  // State for cookie preferences
  const [preferences, setPreferences] = useState({
    [COOKIE_CATEGORIES.ESSENTIAL]: true, // Essential cookies are always enabled
    [COOKIE_CATEGORIES.PREFERENCES]: false,
    [COOKIE_CATEGORIES.ANALYTICS]: false,
    [COOKIE_CATEGORIES.MARKETING]: false
  });

  // Check if consent has been given on component mount
  useEffect(() => {
    const consentData = getConsentData();

    if (!consentData.consented) {
      setShowBanner(true);
    } else {
      // Update preferences state with saved preferences
      setPreferences(consentData.categories);
    }
  }, []);

  // Handle accepting all cookies
  const handleAcceptAll = () => {
    const allConsent = {
      [COOKIE_CATEGORIES.ESSENTIAL]: true,
      [COOKIE_CATEGORIES.PREFERENCES]: true,
      [COOKIE_CATEGORIES.ANALYTICS]: true,
      [COOKIE_CATEGORIES.MARKETING]: true
    };

    saveFullConsent(allConsent);
    setPreferences(allConsent);
    setShowBanner(false);

    // Update analytics consent
    updateAnalyticsConsent(true);
  };

  // Handle accepting only essential cookies
  const handleAcceptEssential = () => {
    const essentialOnly = {
      [COOKIE_CATEGORIES.ESSENTIAL]: true,
      [COOKIE_CATEGORIES.PREFERENCES]: false,
      [COOKIE_CATEGORIES.ANALYTICS]: false,
      [COOKIE_CATEGORIES.MARKETING]: false
    };

    saveFullConsent(essentialOnly);
    setPreferences(essentialOnly);
    setShowBanner(false);

    // Update analytics consent
    updateAnalyticsConsent(false);
  };

  // Handle saving custom preferences
  const handleSavePreferences = () => {
    saveFullConsent(preferences);
    setShowBanner(false);
    setShowPreferences(false);

    // Update analytics consent
    updateAnalyticsConsent(preferences[COOKIE_CATEGORIES.ANALYTICS]);
  };

  // Handle preference toggle
  const handlePreferenceToggle = (category) => {
    setPreferences(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // If banner shouldn't be shown, don't render anything
  if (!showBanner) {
    return null;
  }

  return (
    <div className={styles.cookieConsent}>
      <div className={styles.cookieConsentContent}>
        {!showPreferences ? (
          <div className={styles.compactView}>
            <div className={styles.compactInfo}>
              <h2 className={styles.cookieConsentTitle}>Cookie Notice</h2>
              <p className={styles.cookieConsentText}>
                This site uses cookies to enhance your experience. By continuing, you consent to our use of cookies.
                <span className={styles.cookieConsentLinks}>
                  <a href="/privacy" className={styles.cookieConsentLink}>Privacy Policy</a> |
                  <a href="/cookie-settings" className={styles.cookieConsentLink}>Cookie Settings</a>
                </span>
              </p>
            </div>
            <div className={styles.compactButtons}>
              <button
                className={`${styles.cookieConsentButton} ${styles.secondary}`}
                onClick={() => setShowPreferences(true)}
              >
                Customize
              </button>
              <button
                className={`${styles.cookieConsentButton} ${styles.tertiary}`}
                onClick={handleAcceptEssential}
              >
                Essential Only
              </button>
              <button
                className={`${styles.cookieConsentButton} ${styles.primary}`}
                onClick={handleAcceptAll}
              >
                Accept All
              </button>
            </div>
          </div>
        ) : (
          <div className={styles.preferencesView}>
            <div className={styles.preferencesHeader}>
              <h2 className={styles.cookieConsentTitle}>Cookie Preferences</h2>
              <button
                className={styles.closeButton}
                onClick={() => setShowPreferences(false)}
                aria-label="Close preferences"
              >
                ×
              </button>
            </div>

            <div className={styles.cookiePreferences}>
              <div className={styles.cookiePreferenceItem}>
                <div className={styles.cookiePreferenceHeader}>
                  <h3 className={styles.cookiePreferenceTitle}>Essential Cookies</h3>
                  <div className={`${styles.cookiePreferenceToggle} ${styles.disabled}`}>
                    <input
                      type="checkbox"
                      id="essential"
                      checked={preferences[COOKIE_CATEGORIES.ESSENTIAL]}
                      disabled
                    />
                    <label htmlFor="essential"></label>
                  </div>
                </div>
                <p className={styles.cookiePreferenceDescription}>
                  Necessary for the website to function properly.
                </p>
              </div>

              <div className={styles.cookiePreferenceItem}>
                <div className={styles.cookiePreferenceHeader}>
                  <h3 className={styles.cookiePreferenceTitle}>Preference Cookies</h3>
                  <div className={styles.cookiePreferenceToggle}>
                    <input
                      type="checkbox"
                      id="preferences"
                      checked={preferences[COOKIE_CATEGORIES.PREFERENCES]}
                      onChange={() => handlePreferenceToggle(COOKIE_CATEGORIES.PREFERENCES)}
                    />
                    <label htmlFor="preferences"></label>
                  </div>
                </div>
                <p className={styles.cookiePreferenceDescription}>
                  Remember your preferences (like theme choice).
                </p>
              </div>

              <div className={styles.cookiePreferenceItem}>
                <div className={styles.cookiePreferenceHeader}>
                  <h3 className={styles.cookiePreferenceTitle}>Analytics Cookies</h3>
                  <div className={styles.cookiePreferenceToggle}>
                    <input
                      type="checkbox"
                      id="analytics"
                      checked={preferences[COOKIE_CATEGORIES.ANALYTICS]}
                      onChange={() => handlePreferenceToggle(COOKIE_CATEGORIES.ANALYTICS)}
                    />
                    <label htmlFor="analytics"></label>
                  </div>
                </div>
                <p className={styles.cookiePreferenceDescription}>
                  Help us understand how visitors use our website.
                </p>
              </div>

              <div className={styles.cookiePreferenceItem}>
                <div className={styles.cookiePreferenceHeader}>
                  <h3 className={styles.cookiePreferenceTitle}>Marketing Cookies</h3>
                  <div className={styles.cookiePreferenceToggle}>
                    <input
                      type="checkbox"
                      id="marketing"
                      checked={preferences[COOKIE_CATEGORIES.MARKETING]}
                      onChange={() => handlePreferenceToggle(COOKIE_CATEGORIES.MARKETING)}
                    />
                    <label htmlFor="marketing"></label>
                  </div>
                </div>
                <p className={styles.cookiePreferenceDescription}>
                  Used for displaying relevant advertisements.
                </p>
              </div>
            </div>

            <div className={styles.preferencesFooter}>
              <button
                className={`${styles.cookieConsentButton} ${styles.primary}`}
                onClick={handleSavePreferences}
              >
                Save Preferences
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CookieConsent;
