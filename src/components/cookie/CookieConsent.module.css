.cookieConsent {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-background);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 0.75rem;
  border-top: 1px solid var(--color-border);
}

.cookieConsentContent {
  max-width: 1200px;
  margin: 0 auto;
}

/* Compact view styles */
.compactView {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.compactInfo {
  flex: 1;
}

.compactButtons {
  display: flex;
  gap: 0.5rem;
  flex-shrink: 0;
}

.cookieConsentTitle {
  font-size: 1rem;
  margin: 0 0 0.25rem 0;
  color: var(--color-text);
  font-weight: 600;
}

.cookieConsentText {
  margin: 0;
  line-height: 1.4;
  color: var(--color-text-secondary);
  font-size: 0.85rem;
}

.cookieConsentLinks {
  display: inline-block;
  margin-left: 0.5rem;
}

.cookieConsentButton {
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.8rem;
  white-space: nowrap;
}

.cookieConsentButton.primary {
  background-color: var(--color-primary);
  color: white;
}

.cookieConsentButton.primary:hover {
  background-color: var(--color-primary-dark);
}

.cookieConsentButton.secondary {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.cookieConsentButton.secondary:hover {
  background-color: var(--color-background-alt);
}

.cookieConsentButton.tertiary {
  background-color: var(--color-background-alt);
  color: var(--color-text);
}

.cookieConsentButton.tertiary:hover {
  background-color: var(--color-background-alt-hover);
}

.cookieConsentLink {
  color: var(--color-primary);
  text-decoration: underline;
  font-size: 0.8rem;
  margin: 0 0.25rem;
}

.cookieConsentLink:hover {
  color: var(--color-primary-dark);
}

/* Preferences view styles */
.preferencesView {
  padding: 0.5rem;
}

.preferencesHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: var(--color-text-secondary);
  padding: 0.25rem;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: var(--color-background-alt);
  color: var(--color-text);
}

.preferencesFooter {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.cookiePreferences {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  max-height: 60vh;
  overflow-y: auto;
}

.cookiePreferenceItem {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: 0.75rem;
  background-color: var(--color-background-alt);
}

.cookiePreferenceHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.cookiePreferenceTitle {
  font-size: 0.9rem;
  margin: 0;
  color: var(--color-text);
  font-weight: 600;
}

.cookiePreferenceDescription {
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.cookiePreferenceToggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
  flex-shrink: 0;
}

.cookiePreferenceToggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.cookiePreferenceToggle label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background);
  transition: .3s;
  border-radius: 34px;
  border: 1px solid var(--color-border);
}

.cookiePreferenceToggle label:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: var(--color-text-tertiary);
  transition: .3s;
  border-radius: 50%;
}

.cookiePreferenceToggle input:checked + label {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.cookiePreferenceToggle input:checked + label:before {
  transform: translateX(20px);
  background-color: white;
}

.cookiePreferenceToggle.disabled label {
  opacity: 0.7;
  cursor: not-allowed;
}

/* Responsive styles */
@media (max-width: 768px) {
  .compactView {
    flex-direction: column;
    align-items: stretch;
  }

  .compactButtons {
    justify-content: flex-end;
    margin-top: 0.5rem;
  }

  .cookieConsentLinks {
    display: block;
    margin-left: 0;
    margin-top: 0.25rem;
  }
}

@media (max-width: 480px) {
  .cookieConsent {
    padding: 0.5rem;
  }

  .compactButtons {
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 0.25rem;
  }

  .cookieConsentButton {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
  }

  .cookiePreferenceHeader {
    flex-direction: row;
    align-items: center;
  }
}
