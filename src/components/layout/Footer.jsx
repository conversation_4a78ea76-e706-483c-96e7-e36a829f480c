import { Link } from 'react-router-dom';
import { FiLinkedin, FiGithub, FiMail, FiInstagram, FiYoutube } from 'react-icons/fi';
import { FaBehance } from 'react-icons/fa';
import styles from './Footer.module.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={styles.footer}>
      <div className="container">
        <div className={styles.footerContent}>
          <div className={styles.footerBrand}>
            <h3 className={styles.footerLogo}><PERSON><PERSON><PERSON><PERSON><PERSON></h3>
            <p className={styles.footerTagline}>
              Pushing Boundaries at the Intersection of Technology, Creativity, Education, and Strategic Thinking
            </p>
          </div>

          <div className={styles.footerLinks}>
            <div className={styles.footerLinkGroup}>
              <h4>Navigation</h4>
              <ul>
                <li><Link to="/">Home</Link></li>
                <li><Link to="/about">About</Link></li>
                <li><Link to="/services">Services</Link></li>
                <li><Link to="/portfolio">Portfolio</Link></li>
                <li><Link to="/books">Books</Link></li>
                <li><Link to="/contact">Contact</Link></li>
              </ul>
            </div>

            <div className={styles.footerLinkGroup}>
              <h4>Services</h4>
              <ul>
                <li><Link to="/services#ai-development">AI Development</Link></li>
                <li><Link to="/services#full-stack-development">Full-Stack Development</Link></li>
                <li><Link to="/services#tech-trainer">Tech Trainer & Mentor</Link></li>
                <li><Link to="/services#business-strategy">Business Strategy</Link></li>
                <li><Link to="/services#graphic-design">Graphic Design</Link></li>
                <li><Link to="/services#video-production">Video Production</Link></li>
                <li><Link to="/services#web-design">Web Design</Link></li>
              </ul>
            </div>

            <div className={styles.footerLinkGroup}>
              <h4>Connect</h4>
              <ul>
                <li>
                  <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
                    <FiMail /> Email
                  </a>
                </li>
                <li>
                  <a href="https://linkedin.com/in/hrishikeshmohite" target="_blank" rel="noopener noreferrer">
                    <FiLinkedin /> LinkedIn
                  </a>
                </li>
                <li>
                  <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                    <FiGithub /> GitHub
                  </a>
                </li>
                <li>
                  <a href="https://www.instagram.com/hrishikesh_mohite_/" target="_blank" rel="noopener noreferrer">
                    <FiInstagram /> Instagram
                  </a>
                </li>
                <li>
                  <a href="https://www.youtube.com/@kontentcreate" target="_blank" rel="noopener noreferrer">
                    <FiYoutube /> YouTube
                  </a>
                </li>
                <li>
                  <a href="https://www.behance.net/hrushikmohite" target="_blank" rel="noopener noreferrer">
                    <FaBehance /> Behance
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className={styles.footerBottom}>
          <div className={styles.footerBottomLeft}>
            <p>&copy; {currentYear} Hrishikesh Mohite. All rights reserved.</p>
            <div className={styles.legalLinks}>
              <Link to="/privacy" onClick={(e) => { e.preventDefault(); window.location.href = '/privacy'; }}>Privacy Policy</Link>
            </div>
          </div>
          <p className={styles.ajinkyaLink}>
            <a href="https://ajinkyacreatiion.com" target="_blank" rel="noopener noreferrer">
              Ajinkya Creatiion
            </a>
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
