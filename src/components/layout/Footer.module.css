.footer {
  background-color: var(--color-background-alt);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
  margin-top: var(--spacing-3xl);
}

.footerContent {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  align-items: start;
}

.footerBrand {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.footerLogo {
  font-family: var(--font-secondary);
  font-weight: var(--fw-bold);
  font-size: var(--fs-xl);
  margin-bottom: var(--spacing-sm);
}

.footerTagline {
  color: var(--color-text-light);
  font-size: var(--fs-md);
  line-height: 1.6;
  margin-bottom: 0;
}

.footerLinks {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  justify-content: space-between;
}

.footerLinkGroup {
  width: 100%;
}

.footerLinkGroup h4 {
  font-size: var(--fs-md);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
  position: relative;
}

.footerLinkGroup h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: var(--color-primary);
}

.footerLinkGroup ul {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footerLinkGroup a {
  color: var(--color-text-light);
  transition: color var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.footerLinkGroup a:hover {
  color: var(--color-primary);
}

.footerBottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--color-border);
  color: var(--color-text-lighter);
  font-size: var(--fs-sm);
}

.footerBottomLeft {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.footerBottom a {
  color: var(--color-text-light);
  transition: color var(--transition-fast);
  position: relative;
  padding: var(--spacing-xs) 0;
  font-weight: var(--fw-medium);
  display: inline-block;
}

.footerBottom a::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.footerBottom a:hover {
  color: var(--color-primary);
}

.footerBottom a:hover::after {
  width: 100%;
}

.ajinkyaLink a {
  font-size: var(--fs-md);
  font-weight: var(--fw-semibold);
  letter-spacing: 0.5px;
}

.ajinkyaLink a::after {
  height: 2.5px;
}

.legalLinks {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xs);
}

.legalLinks a {
  color: var(--color-text-lighter);
  font-size: var(--fs-sm);
  transition: color var(--transition-fast);
  position: relative;
}

.legalLinks a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.legalLinks a:hover {
  color: var(--color-primary);
}

.legalLinks a:hover::after {
  width: 100%;
}

@media (max-width: 1200px) {
  .footerContent {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .footerLinks {
    grid-template-columns: repeat(3, 1fr);
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
  }
}

@media (max-width: 992px) {
  .footerLinks {
    grid-template-columns: repeat(3, 1fr);
    max-width: 700px;
  }
}

@media (max-width: 768px) {
  .footerLinks {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
    max-width: 500px;
  }

  .footerLinkGroup:nth-child(3) {
    grid-column: span 2;
    justify-self: center;
    max-width: 200px;
  }

  .footerBottom {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .footerBottomLeft {
    align-items: center;
  }

  .legalLinks {
    justify-content: center;
  }

  .ajinkyaLink {
    margin-top: var(--spacing-xs);
  }

  .ajinkyaLink a {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 576px) {
  .footerLinks {
    grid-template-columns: 1fr;
    max-width: 300px;
  }

  .footerLinkGroup:nth-child(3) {
    grid-column: span 1;
    justify-self: start;
    max-width: none;
  }

  .footerLinkGroup {
    margin-bottom: var(--spacing-md);
  }
}
