import { useState, useEffect } from 'react';
import { Link, NavLink } from 'react-router-dom';
import { useTheme } from '../../hooks/useTheme';
import { FiSun, FiMoon, FiMenu, FiX } from 'react-icons/fi';
import styles from './Header.module.css';

const Header = () => {
  const { theme, toggleTheme } = useTheme();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMenuOpen]);

  return (
    <header className={`${styles.header} ${scrolled ? styles.scrolled : ''}`}>
      <div className={`container ${styles.headerContainer}`}>
        <Link to="/" className={styles.logo} onClick={closeMenu}>
          <span className={styles.logoText}>Hrishikesh Mohite</span>
        </Link>

        <nav className={`${styles.nav} ${isMenuOpen ? styles.navActive : ''}`}>
          <ul className={styles.navList}>
            <li className={styles.navItem}>
              <NavLink 
                to="/" 
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
                end
              >
                Home
              </NavLink>
            </li>
            <li className={styles.navItem}>
              <NavLink 
                to="/about" 
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
              >
                About
              </NavLink>
            </li>
            <li className={styles.navItem}>
              <NavLink 
                to="/services" 
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
              >
                Services
              </NavLink>
            </li>
            <li className={styles.navItem}>
              <NavLink 
                to="/portfolio" 
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
              >
                Portfolio
              </NavLink>
            </li>
            <li className={styles.navItem}>
              <NavLink
                to="/books"
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
              >
                Books
              </NavLink>
            </li>
            <li className={styles.navItem}>
              <NavLink
                to="/courses"
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
              >
                Courses
              </NavLink>
            </li>
            <li className={styles.navItem}>
              <NavLink
                to="/contact"
                className={({ isActive }) => isActive ? styles.activeLink : ''}
                onClick={closeMenu}
              >
                Contact
              </NavLink>
            </li>
          </ul>
        </nav>

        <div className={styles.headerActions}>
          <button 
            className={styles.themeToggle} 
            onClick={toggleTheme} 
            aria-label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
          >
            {theme === 'light' ? <FiMoon size={20} /> : <FiSun size={20} />}
          </button>
          
          <button 
            className={styles.menuToggle} 
            onClick={toggleMenu}
            aria-label={isMenuOpen ? 'Close menu' : 'Open menu'}
          >
            {isMenuOpen ? <FiX size={24} /> : <FiMenu size={24} />}
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
