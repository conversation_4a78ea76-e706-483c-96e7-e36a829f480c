.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 80px;
  z-index: var(--z-fixed);
  background-color: transparent;
  transition: all var(--transition-normal);
}

.scrolled {
  background-color: var(--color-background);
  box-shadow: var(--shadow-md);
  height: 70px;
}

.headerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
}

.logo {
  display: flex;
  align-items: center;
  z-index: var(--z-fixed);
}

.logoText {
  font-family: var(--font-secondary);
  font-weight: var(--fw-bold);
  font-size: var(--fs-lg);
  color: var(--color-text);
  transition: color var(--transition-fast);
}

.nav {
  display: flex;
  align-items: center;
}

.navList {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.navItem a {
  position: relative;
  color: var(--color-text);
  font-weight: var(--fw-medium);
  transition: color var(--transition-fast);
  padding: var(--spacing-xs) 0;
}

.navItem a:hover {
  color: var(--color-primary);
}

.navItem a::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.navItem a:hover::after,
.activeLink::after {
  width: 100% !important;
}

.activeLink {
  color: var(--color-primary) !important;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.themeToggle, 
.menuToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  color: var(--color-text);
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-full);
  transition: all var(--transition-fast);
}

.themeToggle:hover, 
.menuToggle:hover {
  background-color: var(--color-background-alt);
  transform: translateY(0);
}

.menuToggle {
  display: none;
  z-index: var(--z-fixed);
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .menuToggle {
    display: flex;
  }

  .nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background-color: var(--color-background);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: right var(--transition-normal);
    z-index: var(--z-modal);
  }

  .navActive {
    right: 0;
  }

  .navList {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xl);
  }

  .navItem a {
    font-size: var(--fs-xl);
  }
}
