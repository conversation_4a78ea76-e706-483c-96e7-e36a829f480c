import { useState, useEffect } from 'react';
import styles from './AnimatedText.module.css';

const AnimatedText = ({ 
  words = [], 
  speed = 2000, 
  className = '',
  prefix = '',
  suffix = '',
}) => {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [text, setText] = useState('');
  const [delta, setDelta] = useState(300 - Math.random() * 100);

  useEffect(() => {
    if (words.length === 0) return;

    const tick = () => {
      const currentWord = words[currentWordIndex];
      const updatedText = isDeleting
        ? currentWord.substring(0, text.length - 1)
        : currentWord.substring(0, text.length + 1);

      setText(updatedText);

      if (isDeleting) {
        setDelta(prevDelta => prevDelta / 2);
      }

      if (!isDeleting && updatedText === currentWord) {
        setIsDeleting(true);
        setDelta(speed);
      } else if (isDeleting && updatedText === '') {
        setIsDeleting(false);
        setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);
        setDelta(500);
      }
    };

    const timer = setTimeout(tick, delta);
    return () => clearTimeout(timer);
  }, [text, delta, isDeleting, currentWordIndex, words, speed]);

  return (
    <span className={`${styles.animatedText} ${className}`}>
      {prefix}{text}<span className={styles.cursor}>|</span>{suffix}
    </span>
  );
};

export default AnimatedText;
