.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-primary);
  font-weight: var(--fw-medium);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: center;
  border: none;
  outline: none;
  gap: var(--spacing-sm);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.button::before {
  content: '';
  position: absolute;
  top: -100%;
  left: -100%;
  width: 50%;
  height: 300%;
  background: linear-gradient(
    120deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transform: rotate(45deg);
  transition: all 0.5s;
  opacity: 0;
  z-index: -1;
}

.button:hover::before {
  animation: slashGlow 0.8s;
}

/* Variants */
.primary {
  background-color: var(--color-primary);
  color: white;
  border: 2px solid transparent;
}

.primary:hover {
  background-color: var(--color-primary-light);
  color: white;
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.3);
}

.secondary {
  background-color: var(--color-secondary);
  color: white;
  border: 2px solid transparent;
}

.secondary:hover {
  background-color: var(--color-secondary-light);
  color: white;
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.3);
}

.outline {
  background-color: transparent;
  border: 2px solid var(--color-primary);
  color: var(--color-primary);
}

.outline:hover {
  background-color: var(--color-primary);
  color: white;
  transform: translateY(-2px);
}

.ghost {
  background-color: transparent;
  color: var(--color-primary);
}

.ghost:hover {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  transform: translateY(-2px);
}

.text {
  background-color: transparent;
  color: var(--color-primary);
  padding: 0;
}

.text:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

/* Sizes */
.small {
  font-size: var(--fs-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  height: 32px;
}

.medium {
  font-size: var(--fs-base);
  padding: var(--spacing-sm) var(--spacing-lg);
  height: 40px;
}

.large {
  font-size: var(--fs-md);
  padding: var(--spacing-md) var(--spacing-xl);
  height: 48px;
}

/* Full Width */
.fullWidth {
  width: 100%;
}

/* Disabled */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* Animations */
@keyframes slashGlow {
  0% {
    opacity: 0;
    left: -100%;
  }
  20% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    left: 100%;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .button {
    height: auto;
    min-height: 40px;
    padding-top: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    white-space: normal;
    line-height: 1.4;
  }

  .small {
    min-height: 32px;
  }

  .large {
    min-height: 48px;
  }
}

@media (max-width: 480px) {
  .button {
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }
}
