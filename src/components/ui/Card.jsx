import { Link } from 'react-router-dom';
import styles from './Card.module.css';

const Card = ({
  children,
  title,
  subtitle,
  image,
  altText,
  icon,
  to,
  href,
  onClick,
  variant = 'default',
  className = '',
  ...props
}) => {
  const cardContent = (
    <>
      {image && (
        <div className={styles.cardImage}>
          <img src={image} alt={altText || title || 'Card image'} />
        </div>
      )}
      <div className={styles.cardBody}>
        {icon && <div className={styles.cardIcon}>{icon}</div>}
        {title && <h3 className={styles.cardTitle}>{title}</h3>}
        {subtitle && <p className={styles.cardSubtitle}>{subtitle}</p>}
        <div className={styles.cardContent}>
          {children}
        </div>
      </div>
    </>
  );

  const cardClasses = `${styles.card} ${styles[variant]} ${className}`;

  if (to) {
    return (
      <Link to={to} className={cardClasses} {...props}>
        {cardContent}
      </Link>
    );
  }

  if (href) {
    return (
      <a href={href} className={cardClasses} target="_blank" rel="noopener noreferrer" {...props}>
        {cardContent}
      </a>
    );
  }

  if (onClick) {
    return (
      <div className={cardClasses} onClick={onClick} role="button" tabIndex={0} {...props}>
        {cardContent}
      </div>
    );
  }

  return (
    <div className={cardClasses} {...props}>
      {cardContent}
    </div>
  );
};

export default Card;
