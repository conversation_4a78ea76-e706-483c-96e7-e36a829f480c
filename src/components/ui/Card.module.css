.card {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
  text-decoration: none;
  color: var(--color-text);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Card Variants */
.default {
  border: 1px solid var(--color-border);
}

.elevated {
  box-shadow: var(--shadow-lg);
}

.outlined {
  border: 2px solid var(--color-border);
  box-shadow: none;
}

.outlined:hover {
  border-color: var(--color-primary);
}

.service {
  text-align: center;
  padding: var(--spacing-lg);
  position: relative;
  overflow: visible;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 0 0 0 transparent;
}

.service:hover {
  border-color: var(--color-primary);
  box-shadow: 0 0 10px 0 var(--color-primary),
              inset 0 0 0 1px var(--color-primary);
}

.project {
  position: relative;
}

.project .cardBody {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: white;
  padding: var(--spacing-lg);
}

.project .cardTitle,
.project .cardSubtitle {
  color: white;
}

/* Card Elements */
.cardImage {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.project .cardImage {
  width: 100%;
  /* Make project cards square (1:1 aspect ratio) */
  height: 0;
  padding-bottom: 100%;
  position: relative;
}

.cardImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.project .cardImage img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card:hover .cardImage img {
  transform: scale(1.05);
}

.cardBody {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.cardIcon {
  font-size: var(--fs-2xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.service .cardIcon {
  width: 70px;
  height: 70px;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
  margin: 0 auto var(--spacing-lg);
}

.cardTitle {
  font-size: var(--fs-lg);
  font-weight: var(--fw-semibold);
  margin-bottom: var(--spacing-sm);
  color: var(--color-text);
}

.cardSubtitle {
  font-size: var(--fs-sm);
  color: var(--color-text-light);
  margin-bottom: var(--spacing-md);
}

.cardContent {
  flex: 1;
}
