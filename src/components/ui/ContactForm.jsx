import { useState, useEffect, useRef } from 'react';
import emailjs from '@emailjs/browser';
import Button from './Button';
import styles from './ContactForm.module.css';
import { EMAILJS_CONFIG } from '../../config/emailjs';

const ContactForm = () => {
  const MESSAGE_CHAR_LIMIT = 500;
  const form = useRef();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');
  const [remainingChars, setRemainingChars] = useState(MESSAGE_CHAR_LIMIT);
  const [showLimitWarning, setShowLimitWarning] = useState(false);

  // Update remaining characters when message changes
  useEffect(() => {
    const remaining = MESSAGE_CHAR_LIMIT - formData.message.length;
    setRemainingChars(remaining);

    // Show warning when limit is reached, hide it after 3 seconds
    if (remaining === 0 && formData.message.length > 0) {
      setShowLimitWarning(true);
      const timer = setTimeout(() => {
        setShowLimitWarning(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [formData.message]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      newErrors.email = 'Invalid email address';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Map form field names to state property names
    const fieldNameMap = {
      'user_name': 'name',
      'user_email': 'email',
      'subject': 'subject',
      'message': 'message'
    };

    const stateName = fieldNameMap[name] || name;

    // Enforce character limit for message field
    if (name === 'message' && value.length > MESSAGE_CHAR_LIMIT) {
      return; // Prevent input beyond the character limit
    }

    setFormData(prevData => ({
      ...prevData,
      [stateName]: value
    }));

    // Clear error when user starts typing
    if (errors[stateName]) {
      setErrors(prevErrors => ({
        ...prevErrors,
        [stateName]: ''
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitError('');

    try {
      // Validate EmailJS configuration
      if (!EMAILJS_CONFIG.serviceId || EMAILJS_CONFIG.serviceId === 'service_id' ||
          !EMAILJS_CONFIG.templateId || EMAILJS_CONFIG.templateId === 'template_id' ||
          !EMAILJS_CONFIG.publicKey || EMAILJS_CONFIG.publicKey === 'public_key') {
        console.error('EmailJS configuration is incomplete. Please check src/config/emailjs.js');
        throw new Error('EmailJS configuration error');
      }

      // Log EmailJS configuration (remove in production)
      console.log('EmailJS Config:', {
        serviceId: EMAILJS_CONFIG.serviceId,
        templateId: EMAILJS_CONFIG.templateId,
        publicKey: EMAILJS_CONFIG.publicKey ? 'Valid (hidden for security)' : 'Invalid',
        recipientEmail: EMAILJS_CONFIG.recipientEmail
      });

      // Add timestamp and source to the form data
      const timestampField = document.createElement('input');
      timestampField.type = 'hidden';
      timestampField.name = 'submission_time';
      timestampField.value = new Date().toLocaleString();
      form.current.appendChild(timestampField);

      const sourceField = document.createElement('input');
      sourceField.type = 'hidden';
      sourceField.name = 'source';
      sourceField.value = 'Portfolio Website Contact Form';
      form.current.appendChild(sourceField);

      // Log form data for debugging (remove in production)
      console.log('Form data being submitted:', {
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message.substring(0, 20) + '...',
        recipient: EMAILJS_CONFIG.recipientEmail
      });

      // Send email using EmailJS
      const result = await emailjs.sendForm(
        EMAILJS_CONFIG.serviceId,
        EMAILJS_CONFIG.templateId,
        form.current,
        EMAILJS_CONFIG.publicKey
      );

      console.log('EmailJS response:', result);

      // Clean up the dynamically added fields
      if (form.current) {
        const timestampField = form.current.querySelector('input[name="submission_time"]');
        const sourceField = form.current.querySelector('input[name="source"]');
        if (timestampField) form.current.removeChild(timestampField);
        if (sourceField) form.current.removeChild(sourceField);
      }

      if (result.text === 'OK') {
        setSubmitSuccess(true);
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });

        // Reset success message after 5 seconds
        setTimeout(() => {
          setSubmitSuccess(false);
        }, 5000);
      } else {
        console.error('EmailJS returned non-OK response:', result);
        throw new Error(`Email sending failed with status: ${result.text}`);
      }
    } catch (error) {
      console.error('Form submission error:', error);

      // Provide more specific error message based on the error
      if (error.message.includes('configuration error')) {
        setSubmitError('Contact form is not properly configured. Please contact the administrator.');
      } else if (error.name === 'NetworkError' || error.message.includes('network')) {
        setSubmitError('Network error. Please check your internet connection and try again.');
      } else {
        setSubmitError(`There was an error submitting the form: ${error.message}. Please try again.`);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.formContainer}>
      {submitSuccess ? (
        <div className={styles.successMessage}>
          <h3>Thank you for your message!</h3>
          <p>I'll get back to you as soon as possible.</p>
        </div>
      ) : (
        <form ref={form} className={styles.form} onSubmit={handleSubmit}>
          {/* Hidden field for recipient email - not visible to users */}
          <input
            type="hidden"
            name="recipient_email"
            value={EMAILJS_CONFIG.recipientEmail}
          />

          <div className={styles.formGroup}>
            <label htmlFor="user_name">Name</label>
            <input
              type="text"
              id="user_name"
              name="user_name"
              value={formData.name}
              onChange={handleChange}
              className={errors.name ? styles.inputError : ''}
            />
            {errors.name && <span className={styles.errorMessage}>{errors.name}</span>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="user_email">Email</label>
            <input
              type="email"
              id="user_email"
              name="user_email"
              value={formData.email}
              onChange={handleChange}
              className={errors.email ? styles.inputError : ''}
            />
            {errors.email && <span className={styles.errorMessage}>{errors.email}</span>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="subject">Subject</label>
            <select
              id="subject"
              name="subject"
              value={formData.subject}
              onChange={handleChange}
              className={errors.subject ? styles.inputError : ''}
            >
              <option value="">Select a subject</option>
              <option value="AI Development & Strategy">AI Development & Strategy</option>
              <option value="Full-Stack Development">Full-Stack Development</option>
              <option value="Tech Trainer & Mentor">Tech Trainer & Mentor</option>
              <option value="Business Strategy">Business Strategy</option>
              <option value="Graphic Design">Graphic Design</option>
              <option value="Video Production">Video Production</option>
              <option value="Web Design">Web Design</option>
              <option value="Other">Other</option>
            </select>
            {errors.subject && <span className={styles.errorMessage}>{errors.subject}</span>}
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="message">Message</label>
            <textarea
              id="message"
              name="message"
              rows="5"
              value={formData.message}
              onChange={handleChange}
              className={`${errors.message ? styles.inputError : ''} ${remainingChars === 0 ? styles.limitReached : ''}`}
              maxLength={MESSAGE_CHAR_LIMIT}
              aria-describedby="message-counter"
            ></textarea>
            <div className={styles.messageFooter}>
              <span
                id="message-counter"
                className={`${styles.charCounter} ${remainingChars <= 50 ? styles.charCounterWarning : ''} ${remainingChars === 0 ? styles.charCounterLimit : ''}`}
              >
                {remainingChars} characters remaining
              </span>
              {showLimitWarning && (
                <span className={styles.limitWarning}>
                  Character limit reached
                </span>
              )}
            </div>
            {errors.message && <span className={styles.errorMessage}>{errors.message}</span>}
          </div>

          {submitError && <div className={styles.submitError}>{submitError}</div>}

          <Button
            type="submit"
            variant="primary"
            size="large"
            fullWidth
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </Button>
        </form>
      )}
    </div>
  );
};

export default ContactForm;
