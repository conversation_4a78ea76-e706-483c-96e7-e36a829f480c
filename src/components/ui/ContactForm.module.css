.formContainer {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-xl);
  width: 100%;
}

.form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.formGroup label {
  font-weight: var(--fw-medium);
  color: var(--color-text);
  font-size: var(--fs-sm);
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  padding: var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--fs-base);
  background-color: var(--color-background);
  color: var(--color-text);
  transition: border-color var(--transition-fast);
  width: 100%;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.inputError {
  border-color: var(--color-error) !important;
}

.errorMessage {
  color: var(--color-error);
  font-size: var(--fs-xs);
  margin-top: var(--spacing-xs);
}

.submitError {
  color: var(--color-error);
  background-color: rgba(var(--color-error-rgb), 0.1);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
  text-align: center;
}

.successMessage {
  text-align: center;
  padding: var(--spacing-xl) 0;
}

.successMessage h3 {
  color: var(--color-success);
  margin-bottom: var(--spacing-md);
}

/* Character counter styles */
.messageFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xs);
}

.charCounter {
  font-size: var(--fs-xs);
  color: var(--color-text-light);
  transition: color var(--transition-fast);
}

.charCounterWarning {
  color: var(--color-warning);
}

.charCounterLimit {
  color: var(--color-error);
  font-weight: var(--fw-medium);
}

.limitWarning {
  font-size: var(--fs-xs);
  color: var(--color-error);
  font-weight: var(--fw-medium);
  animation: fadeIn 0.3s ease-in-out;
}

.limitReached {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 1px var(--color-error) !important;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (max-width: 768px) {
  .formContainer {
    padding: var(--spacing-lg);
  }
}
