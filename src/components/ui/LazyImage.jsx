import { useState, useEffect, useRef } from 'react';

/**
 * LazyImage component that only loads images when they enter the viewport
 *
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Image alt text
 * @param {string} props.className - Optional CSS class
 * @param {Object} props.style - Optional inline styles
 * @param {string} props.placeholderSrc - Optional placeholder image to show while loading
 * @param {Function} props.onLoad - Optional callback when image loads
 * @param {string} props.objectFit - Optional object-fit style (cover, contain, etc.)
 * @returns {JSX.Element} - Rendered component
 */
const LazyImage = ({
  src,
  alt,
  className = '',
  style = {},
  placeholderSrc = '',
  onLoad,
  objectFit = 'cover',
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef(null);

  useEffect(() => {
    // Reset loaded state when src changes
    setIsLoaded(false);
  }, [src]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          // Once we've seen it, no need to keep observing
          if (imgRef.current) {
            observer.unobserve(imgRef.current);
          }
        }
      },
      {
        rootMargin: '200px', // Start loading when image is 200px from viewport
        threshold: 0.01,
      }
    );

    const currentImgRef = imgRef.current;
    if (currentImgRef) {
      observer.observe(currentImgRef);
    }

    return () => {
      if (currentImgRef) {
        observer.unobserve(currentImgRef);
      }
    };
  }, []);

  const handleImageLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  const imageStyles = {
    ...style,
    objectFit,
    opacity: isLoaded ? 1 : 0,
    transition: 'opacity 0.3s ease-in-out',
    // Enhanced color fidelity preservation
    filter: 'none',
    WebkitFilter: 'none',
    colorRendering: 'optimizeQuality',
    colorInterpolation: 'linearRGB',
    colorAdjust: 'exact',
    imageRendering: 'auto',
    // Prevent any color space conversion issues
    colorSpace: 'srgb',
  };

  const placeholderStyles = {
    ...style,
    objectFit,
    opacity: isLoaded ? 0 : 1,
    transition: 'opacity 0.3s ease-in-out',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  };

  return (
    <div
      ref={imgRef}
      style={{
        position: 'relative',
        overflow: 'hidden',
        width: '100%',
        height: '100%',
      }}
    >
      {placeholderSrc && (
        <img
          src={placeholderSrc}
          alt={`${alt} placeholder`}
          style={placeholderStyles}
          className={className}
        />
      )}

      {isInView && (
        <img
          src={src}
          alt={alt}
          className={className}
          style={imageStyles}
          onLoad={handleImageLoad}
          loading="lazy"
          {...props}
        />
      )}
    </div>
  );
};

export default LazyImage;
