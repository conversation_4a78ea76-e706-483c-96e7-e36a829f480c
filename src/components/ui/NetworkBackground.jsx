import { useRef, useEffect, useState } from 'react';
import { useTheme } from '../../hooks/useTheme';
import { drawShape } from '../../utils/shapes';
import styles from './NetworkBackground.module.css';

const NetworkBackground = ({
  color = '#0070f3',
  shape = 'default',
  pointCount = 80,
  connectionDistance = 175,
  baseOpacity = 0.4,
  highlightOpacity = 0.9,
}) => {
  const canvasRef = useRef(null);
  const { theme } = useTheme();
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [mousePosition, setMousePosition] = useState({ x: null, y: null });
  const pointsRef = useRef([]);
  const animationRef = useRef(null);
  const containerRef = useRef(null);

  // Set up canvas dimensions
  useEffect(() => {
    if (!containerRef.current) return;

    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  // Initialize points
  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return;

    // Create random points
    pointsRef.current = Array.from({ length: pointCount }, () => ({
      x: Math.random() * dimensions.width,
      y: Math.random() * dimensions.height,
      vx: (Math.random() - 0.5) * 0.5, // Velocity X (-0.25 to 0.25)
      vy: (Math.random() - 0.5) * 0.5, // Velocity Y (-0.25 to 0.25)
      radius: Math.random() * 2 + 1,   // Random radius between 1-3
    }));
  }, [dimensions, pointCount]);

  // Mouse move handler
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    };

    const handleMouseLeave = () => {
      setMousePosition({ x: null, y: null });
    };

    const currentContainerRef = containerRef.current;
    if (currentContainerRef) {
      currentContainerRef.addEventListener('mousemove', handleMouseMove);
      currentContainerRef.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (currentContainerRef) {
        currentContainerRef.removeEventListener('mousemove', handleMouseMove);
        currentContainerRef.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // Animation loop
  useEffect(() => {
    if (!canvasRef.current || dimensions.width === 0 || dimensions.height === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    const animate = () => {
      ctx.clearRect(0, 0, dimensions.width, dimensions.height);

      // Update points position
      pointsRef.current.forEach(point => {
        point.x += point.vx;
        point.y += point.vy;

        // Bounce off edges
        if (point.x < 0 || point.x > dimensions.width) point.vx *= -1;
        if (point.y < 0 || point.y > dimensions.height) point.vy *= -1;
      });

      // Draw connections
      ctx.lineWidth = 1;
      pointsRef.current.forEach((point, i) => {
        for (let j = i + 1; j < pointsRef.current.length; j++) {
          const point2 = pointsRef.current[j];
          const dx = point.x - point2.x;
          const dy = point.y - point2.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < connectionDistance) {
            // Calculate opacity based on distance
            const opacity = baseOpacity * (1 - distance / connectionDistance);

            // Check if points are near mouse
            let highlightFactor = 1;
            if (mousePosition.x !== null && mousePosition.y !== null) {
              const mouseDx = mousePosition.x - point.x;
              const mouseDy = mousePosition.y - point.y;
              const mouseDistance = Math.sqrt(mouseDx * mouseDx + mouseDy * mouseDy);

              const mouseDx2 = mousePosition.x - point2.x;
              const mouseDy2 = mousePosition.y - point2.y;
              const mouseDistance2 = Math.sqrt(mouseDx2 * mouseDx2 + mouseDy2 * mouseDy2);

              if (mouseDistance < 200 || mouseDistance2 < 200) {
                const closestDistance = Math.min(mouseDistance, mouseDistance2);
                highlightFactor = 1 + ((200 - closestDistance) / 200) * ((highlightOpacity / baseOpacity) - 1);
              }
            }

            ctx.strokeStyle = `${color}${Math.floor(opacity * highlightFactor * 255).toString(16).padStart(2, '0')}`;
            ctx.beginPath();
            ctx.moveTo(point.x, point.y);
            ctx.lineTo(point2.x, point2.y);
            ctx.stroke();
          }
        }
      });

      // Draw points
      pointsRef.current.forEach(point => {
        ctx.beginPath();

        // Check if point is near mouse
        let pointOpacity = baseOpacity;
        if (mousePosition.x !== null && mousePosition.y !== null) {
          const dx = mousePosition.x - point.x;
          const dy = mousePosition.y - point.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 200) {
            pointOpacity = baseOpacity + ((200 - distance) / 200) * (highlightOpacity - baseOpacity);
          }
        }

        ctx.fillStyle = `${color}${Math.floor(pointOpacity * 255).toString(16).padStart(2, '0')}`;
        ctx.arc(point.x, point.y, point.radius, 0, Math.PI * 2);
        ctx.fill();
      });

      // Draw shape
      if (shape !== 'default') {
        drawShape(ctx, shape, dimensions.width, dimensions.height, color, mousePosition);
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions, mousePosition, color, shape, connectionDistance, baseOpacity, highlightOpacity, theme]);

  return (
    <div ref={containerRef} className={styles.container}>
      <canvas
        ref={canvasRef}
        width={dimensions.width}
        height={dimensions.height}
        className={styles.canvas}
      />
    </div>
  );
};

export default NetworkBackground;
