import { useRef, useState, useEffect, useMemo } from 'react';
import { useTheme } from '../../hooks/useTheme';
import styles from './HeroBackground.module.css';

const OptimizedHeroBackground = ({
  primaryColor = '#0070f3',
  secondaryColor = '#3291ff',
  pointCount = 60, // Reduced from 120
  connectionDistance = 120, // Reduced from 150
  baseOpacity = 0.3,
  highlightOpacity = 0.8,
  particleSize = { min: 1, max: 3 },
  particleSpeed = { min: 0.1, max: 0.3 }, // Reduced speed
  dataNodeCount = 3, // Reduced from 6
  dataNodeSize = 5, // Reduced from 6
}) => {
  const canvasRef = useRef(null);
  // We don't need theme here but keeping the import
  useTheme();
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [mousePosition, setMousePosition] = useState({ x: null, y: null });
  const pointsRef = useRef([]);
  const dataNodesRef = useRef([]);
  const animationRef = useRef(null);
  const containerRef = useRef(null);
  const isVisibleRef = useRef(false);
  const lastFrameTimeRef = useRef(0);
  // const targetFpsRef = useRef(30); // Target 30 FPS instead of 60 - not currently used
  const frameIntervalRef = useRef(1000 / 30); // ~33ms between frames

  // Colors based on theme using useMemo to prevent re-renders
  const colors = useMemo(() => ({
    primary: primaryColor,
    secondary: secondaryColor,
  }), [primaryColor, secondaryColor]);

  // Set up canvas dimensions and resize handler
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);

    return () => {
      window.removeEventListener('resize', updateDimensions);
    };
  }, []);

  // Handle mouse movement
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    };

    const handleMouseLeave = () => {
      setMousePosition({ x: null, y: null });
    };

    const currentContainerRef = containerRef.current;
    if (currentContainerRef) {
      currentContainerRef.addEventListener('mousemove', handleMouseMove);
      currentContainerRef.addEventListener('mouseleave', handleMouseLeave);
    }

    return () => {
      if (currentContainerRef) {
        currentContainerRef.removeEventListener('mousemove', handleMouseMove);
        currentContainerRef.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // Intersection Observer to only animate when visible
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        isVisibleRef.current = entries[0].isIntersecting;
      },
      { threshold: 0.1 }
    );

    const currentContainerRef = containerRef.current;
    observer.observe(currentContainerRef);

    return () => {
      if (currentContainerRef) {
        observer.unobserve(currentContainerRef);
      }
    };
  }, []);

  // Initialize particles
  useEffect(() => {
    if (dimensions.width === 0 || dimensions.height === 0) return;

    // Create regular particles
    pointsRef.current = Array.from({ length: pointCount }, () => ({
      x: Math.random() * dimensions.width,
      y: Math.random() * dimensions.height,
      vx: (Math.random() * (particleSpeed.max - particleSpeed.min) + particleSpeed.min) * (Math.random() > 0.5 ? 1 : -1),
      vy: (Math.random() * (particleSpeed.max - particleSpeed.min) + particleSpeed.min) * (Math.random() > 0.5 ? 1 : -1),
      radius: Math.random() * (particleSize.max - particleSize.min) + particleSize.min,
      color: Math.random() > 0.5 ? colors.primary : colors.secondary,
    }));

    // Create data nodes (larger, more prominent nodes)
    dataNodesRef.current = Array.from({ length: dataNodeCount }, () => ({
      x: Math.random() * dimensions.width,
      y: Math.random() * dimensions.height,
      vx: (Math.random() * 0.2 + 0.1) * (Math.random() > 0.5 ? 1 : -1),
      vy: (Math.random() * 0.2 + 0.1) * (Math.random() > 0.5 ? 1 : -1),
      radius: dataNodeSize,
      pulsePhase: Math.random() * Math.PI * 2,
      color: colors.secondary,
    }));
  }, [dimensions, pointCount, dataNodeCount, colors, particleSize, particleSpeed, dataNodeSize]);

  // Animation loop with throttling
  useEffect(() => {
    if (!canvasRef.current || dimensions.width === 0 || dimensions.height === 0) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d', { alpha: true });

    const animate = (timestamp) => {
      // Skip frames to maintain target FPS
      const elapsed = timestamp - lastFrameTimeRef.current;
      if (elapsed < frameIntervalRef.current || !isVisibleRef.current) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      lastFrameTimeRef.current = timestamp - (elapsed % frameIntervalRef.current);

      ctx.clearRect(0, 0, dimensions.width, dimensions.height);

      // Update particle positions
      pointsRef.current.forEach(point => {
        point.x += point.vx;
        point.y += point.vy;

        // Bounce off edges
        if (point.x < 0 || point.x > dimensions.width) point.vx *= -1;
        if (point.y < 0 || point.y > dimensions.height) point.vy *= -1;
      });

      // Update data node positions and pulse phase
      dataNodesRef.current.forEach(node => {
        node.x += node.vx;
        node.y += node.vy;
        node.pulsePhase += 0.02; // Slower pulsing

        // Bounce off edges
        if (node.x < 0 || node.x > dimensions.width) node.vx *= -1;
        if (node.y < 0 || node.y > dimensions.height) node.vy *= -1;
      });

      // Draw connections between particles (with reduced connections)
      for (let i = 0; i < pointsRef.current.length; i++) {
        const pointA = pointsRef.current[i];

        // Only check every other particle to reduce calculations
        for (let j = i + 2; j < pointsRef.current.length; j += 2) {
          const pointB = pointsRef.current[j];
          const dx = pointA.x - pointB.x;
          const dy = pointA.y - pointB.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < connectionDistance) {
            // Calculate opacity based on distance
            const opacity = baseOpacity * (1 - distance / connectionDistance);

            // Draw connection line
            ctx.beginPath();
            ctx.strokeStyle = `${pointA.color}${Math.floor(opacity * 255).toString(16).padStart(2, '0')}`;
            ctx.moveTo(pointA.x, pointA.y);
            ctx.lineTo(pointB.x, pointB.y);
            ctx.stroke();
          }
        }
      }

      // Draw regular particles
      pointsRef.current.forEach(point => {
        ctx.beginPath();
        ctx.fillStyle = `${point.color}${Math.floor(baseOpacity * 255).toString(16).padStart(2, '0')}`;
        ctx.arc(point.x, point.y, point.radius, 0, Math.PI * 2);
        ctx.fill();
      });

      // Draw data nodes with simplified effects
      dataNodesRef.current.forEach(node => {
        ctx.beginPath();
        const pulseScale = 1 + 0.2 * Math.sin(node.pulsePhase);
        const pulseRadius = node.radius * pulseScale;

        // Simplified glow (no shadow)
        ctx.fillStyle = `${node.color}cc`;
        ctx.arc(node.x, node.y, pulseRadius, 0, Math.PI * 2);
        ctx.fill();

        // Inner circle
        ctx.beginPath();
        ctx.fillStyle = node.color;
        ctx.arc(node.x, node.y, pulseRadius * 0.6, 0, Math.PI * 2);
        ctx.fill();
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [dimensions, mousePosition, colors, connectionDistance, baseOpacity, highlightOpacity, particleSpeed]);

  return (
    <div ref={containerRef} className={styles.container}>
      <canvas
        ref={canvasRef}
        width={dimensions.width}
        height={dimensions.height}
        className={styles.canvas}
      />
    </div>
  );
};

export default OptimizedHeroBackground;
