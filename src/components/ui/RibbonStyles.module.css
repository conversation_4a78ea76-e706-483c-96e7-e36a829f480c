.ribbon {
  width: 100%;
  padding: var(--spacing-md) 0;
  background-color: var(--color-background);
  position: relative;
  overflow: hidden;
}

.dark {
  background-color: var(--color-background-dark);
}

.light {
  background-color: var(--color-background-light);
}

.ribbonContainer {
  width: 100%;
  overflow: hidden;
  position: relative;
  padding: var(--spacing-lg) 0;
}

.ribbonContainer::before,
.ribbonContainer::after {
  content: '';
  position: absolute;
  top: 0;
  width: 100px;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.ribbonContainer::before {
  left: 0;
  background: linear-gradient(to right, var(--color-background), transparent);
}

.ribbonContainer::after {
  right: 0;
  background: linear-gradient(to left, var(--color-background), transparent);
}

.dark .ribbonContainer::before {
  background: linear-gradient(to right, var(--color-background-dark), transparent);
}

.dark .ribbonContainer::after {
  background: linear-gradient(to left, var(--color-background-dark), transparent);
}

.light .ribbonContainer::before {
  background: linear-gradient(to right, var(--color-background-light), transparent);
}

.light .ribbonContainer::after {
  background: linear-gradient(to left, var(--color-background-light), transparent);
}

.ribbonTrack {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  width: max-content;
  /* Add extra padding to ensure smooth looping */
  padding-right: var(--spacing-md);
  /* Ensure the animation starts immediately */
  animation-play-state: running !important;
  /* Optimize animation performance */
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.animate .ribbonTrack {
  animation: slideTrack 120s linear infinite;
  /* Use will-change to improve performance */
  will-change: transform;
  /* Ensure hardware acceleration for smoother animation */
  transform: translateZ(0);
  backface-visibility: hidden;
}

.animateReverse .ribbonTrack {
  animation: slideTrackReverse 100s linear infinite;
  /* Use will-change to improve performance */
  will-change: transform;
  /* Ensure hardware acceleration for smoother animation */
  transform: translateZ(0);
  backface-visibility: hidden;
}

@keyframes slideTrack {
  /* Right to left animation */
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes slideTrackReverse {
  /* Left to right animation */
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.ribbonItem {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-normal);
}

.ribbonItem:hover {
  transform: translateY(-5px);
}

.logoContainer {
  width: 90px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  filter: grayscale(100%);
  opacity: 0.7;
  transition: filter 0.4s ease, opacity 0.4s ease, transform 0.4s ease;
  padding: 10px;
  border-radius: 8px;
  margin: 10px;
}

.ribbonItem:hover .logoContainer {
  filter: grayscale(0%);
  opacity: 1;
  transform: scale(1.1);
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.logoContainer img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.ribbonItem:hover .logoContainer img {
  transform: scale(1.1);
}

/* Removed itemName styles as we no longer display text labels */

/* Specific ribbon styles */
.techRibbon {
  border-bottom: 1px solid var(--color-border);
}

.toolsRibbon {
  border-top: 1px solid var(--color-border);
}

/* Responsive styles */
@media (max-width: 768px) {
  .logoContainer {
    width: 75px;
    height: 75px;
    margin: 8px;
  }

  .ribbonTrack {
    gap: var(--spacing-lg);
  }

  .animate .ribbonTrack {
    animation: slideTrack 110s linear infinite;
  }

  .animateReverse .ribbonTrack {
    animation: slideTrackReverse 95s linear infinite;
  }

  /* Adjust keyframes for tablet screens */
  @keyframes slideTrack {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  @keyframes slideTrackReverse {
    0% {
      transform: translateX(-50%);
    }
    100% {
      transform: translateX(0);
    }
  }
}

@media (max-width: 480px) {
  .logoContainer {
    width: 65px;
    height: 65px;
    margin: 6px;
    padding: 8px;
  }

  .ribbonTrack {
    gap: var(--spacing-md);
  }

  .animate .ribbonTrack {
    animation: slideTrack 100s linear infinite;
  }

  .animateReverse .ribbonTrack {
    animation: slideTrackReverse 90s linear infinite;
  }

  /* Adjust keyframes for mobile screens */
  @keyframes slideTrack {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }

  @keyframes slideTrackReverse {
    0% {
      transform: translateX(-50%);
    }
    100% {
      transform: translateX(0);
    }
  }
}
