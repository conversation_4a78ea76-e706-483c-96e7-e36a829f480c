import { useState } from 'react';
import LazyImage from './LazyImage';

/**
 * SEOImage component that extends LazyImage with enhanced SEO attributes
 *
 * @param {Object} props - Component props
 * @param {string} props.src - Image source URL
 * @param {string} props.alt - Image alt text (will be enhanced with <PERSON><PERSON><PERSON><PERSON><PERSON>'s name if not present)
 * @param {string} props.title - Optional image title attribute for SEO
 * @param {string} props.className - Optional CSS class
 * @param {Object} props.style - Optional inline styles
 * @param {string} props.placeholderSrc - Optional placeholder image to show while loading
 * @param {Function} props.onLoad - Optional callback when image loads
 * @param {string} props.objectFit - Optional object-fit style (cover, contain, etc.)
 * @param {string} props.keywords - Optional comma-separated keywords for the image
 * @returns {JSX.Element} - Rendered component
 */
const SEOImage = ({
  src,
  alt,
  title,
  className = '',
  style = {},
  placeholderSrc = '',
  onLoad,
  objectFit = 'cover',
  keywords = '',
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);

  // Enhance alt text with Hrishikesh Mohite's name if not already present
  const enhancedAlt = alt.includes('Hrishikesh Mohite')
    ? alt
    : `Hrishikesh Mohite - ${alt}`;

  // Generate title if not provided
  const imageTitle = title || enhancedAlt;

  // Handle image load
  const handleImageLoad = () => {
    setIsLoaded(true);
    if (onLoad) onLoad();
  };

  return (
    <LazyImage
      src={src}
      alt={enhancedAlt}
      className={className}
      style={style}
      placeholderSrc={placeholderSrc}
      onLoad={handleImageLoad}
      objectFit={objectFit}
      title={imageTitle}
      data-keywords={keywords}
      loading="lazy"
      decoding="async"
      fetchPriority={isLoaded ? "auto" : "high"}
      {...props}
    />
  );
};

export default SEOImage;
