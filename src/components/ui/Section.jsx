import styles from './Section.module.css';

const Section = ({ 
  children, 
  id, 
  className = '', 
  title, 
  subtitle,
  centered = false,
  fullHeight = false,
  padding = 'normal',
  background = 'none',
  ...props 
}) => {
  return (
    <section 
      id={id}
      className={`
        ${styles.section} 
        ${styles[`padding-${padding}`]} 
        ${styles[`bg-${background}`]}
        ${fullHeight ? styles.fullHeight : ''}
        ${className}
      `}
      {...props}
    >
      <div className="container">
        {(title || subtitle) && (
          <div className={`${styles.sectionHeader} ${centered ? styles.centered : ''}`}>
            {title && <h2 className={styles.sectionTitle}>{title}</h2>}
            {subtitle && <p className={styles.sectionSubtitle}>{subtitle}</p>}
          </div>
        )}
        <div className={styles.sectionContent}>
          {children}
        </div>
      </div>
    </section>
  );
};

export default Section;
