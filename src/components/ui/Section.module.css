.section {
  width: 100%;
  position: relative;
}

/* Padding Variations */
.padding-small {
  padding: var(--spacing-lg) 0;
}

.padding-normal {
  padding: var(--spacing-2xl) 0;
}

.padding-large {
  padding: var(--spacing-3xl) 0;
}

/* Background Variations */
.bg-none {
  background-color: transparent;
}

.bg-light {
  background-color: var(--color-background-alt);
}

.bg-dark {
  background-color: var(--color-background-dark);
}

.bg-primary {
  background-color: var(--color-primary);
  color: white;
}

.bg-secondary {
  background-color: var(--color-secondary);
  color: white;
}

/* Full Height */
.fullHeight {
  min-height: calc(100vh - 80px); /* Subtract header height */
  display: flex;
  align-items: center;
}

/* Section Header */
.sectionHeader {
  margin-bottom: var(--spacing-xl);
}

.centered {
  text-align: center;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.sectionTitle {
  position: relative;
  display: inline-block;
  margin-bottom: var(--spacing-md);
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--color-primary);
}

.centered .sectionTitle::after {
  left: 50%;
  transform: translateX(-50%);
}

.sectionSubtitle {
  font-size: var(--fs-lg);
  color: var(--color-text-light);
  margin-top: var(--spacing-md);
}

/* Section Content */
.sectionContent {
  width: 100%;
}
