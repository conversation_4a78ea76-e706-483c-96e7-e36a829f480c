import { useEffect, useState } from 'react';
import { useTheme } from '../../hooks/useTheme';
import styles from './RibbonStyles.module.css';

// Import technology logos
import reactLogo from '../../assets/images/tech-logos/react.svg';
import electronLogo from '../../assets/images/tech-logos/electron.svg';
import viteLogo from '../../assets/images/tech-logos/vite.svg';
import nodejsLogo from '../../assets/images/tech-logos/nodejs.svg';
import astroLogo from '../../assets/images/tech-logos/astro.svg';
import javascriptLogo from '../../assets/images/tech-logos/javascript.svg';
import typescriptLogo from '../../assets/images/tech-logos/typescript.svg';
import vueLogo from '../../assets/images/tech-logos/vue.svg';
import kotlinLogo from '../../assets/images/tech-logos/kotlin.svg';
import azureLogo from '../../assets/images/tech-logos/azure.svg';
import microsoftLogo from '../../assets/images/tech-logos/microsoft.svg';
import chatgptLogo from '../../assets/images/tech-logos/chatgpt.svg';
import awsLogo from '../../assets/images/tech-logos/icons8-amazon-web-services.svg';
import dockerLogo from '../../assets/images/tech-logos/icons8-docker.svg';
import htmlLogo from '../../assets/images/tech-logos/icons8-html5.svg';
import cssLogo from '../../assets/images/tech-logos/icons8-css3.svg';

/**
 * TechRibbon component displays a continuously scrolling ribbon of technology logos
 * with grayscale to color hover effect
 */
const TechRibbon = () => {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = useState(false);

  // Technology stack with logos, names, and SEO-optimized alt text
  const techItems = [
    { name: 'React', logo: reactLogo, alt: 'React JavaScript library logo - Frontend development framework used by Hrishikesh Mohite for building modern web applications' },
    { name: 'Electron', logo: electronLogo, alt: 'Electron framework logo - Cross-platform desktop app development technology for full-stack solutions' },
    { name: 'Vite', logo: viteLogo, alt: 'Vite build tool logo - Modern frontend build tool for fast development and optimized production builds' },
    { name: 'Node.js', logo: nodejsLogo, alt: 'Node.js runtime logo - JavaScript backend runtime environment for full-stack development and API creation' },
    { name: 'Astro', logo: astroLogo, alt: 'Astro web framework logo - Modern static site generator for high-performance web development' },
    { name: 'JavaScript', logo: javascriptLogo, alt: 'JavaScript programming language logo - Core web development language for frontend and backend solutions' },
    { name: 'TypeScript', logo: typescriptLogo, alt: 'TypeScript programming language logo - Strongly typed JavaScript for enterprise-level full-stack development' },
    { name: 'Vue.js', logo: vueLogo, alt: 'Vue.js framework logo - Progressive JavaScript framework for building user interfaces and web applications' },
    { name: 'Kotlin', logo: kotlinLogo, alt: 'Kotlin programming language logo - Modern programming language for Android development and cross-platform solutions' },
    { name: 'Azure', logo: azureLogo, alt: 'Microsoft Azure cloud platform logo - Cloud computing services for AI development and enterprise solutions' },
    { name: 'Microsoft', logo: microsoftLogo, alt: 'Microsoft technology logo - Microsoft Copilot and enterprise technology solutions for business transformation' },
    { name: 'ChatGPT', logo: chatgptLogo, alt: 'ChatGPT AI logo - OpenAI ChatGPT integration for AI in education and custom AI solutions' },
    { name: 'AWS', logo: awsLogo, alt: 'Amazon Web Services logo - Cloud infrastructure platform for scalable AI development and full-stack applications' },
    { name: 'Docker', logo: dockerLogo, alt: 'Docker containerization logo - Container technology for DevOps and scalable application deployment' },
    { name: 'HTML5', logo: htmlLogo, alt: 'HTML5 markup language logo - Modern web markup language for semantic web development and accessibility' },
    { name: 'CSS3', logo: cssLogo, alt: 'CSS3 styling language logo - Advanced styling technology for responsive web design and user experience' },
  ];

  // Create a duplicated array for seamless looping
  // With more logos, we need to ensure we have enough repetition for smooth transitions
  // Using four copies instead of three to ensure no visible jumps during animation
  const technologies = [...techItems, ...techItems, ...techItems, ...techItems];

  // Set animation to always be visible
  useEffect(() => {
    // Immediately set to visible without waiting for intersection
    setIsVisible(true);
  }, []);

  return (
    <div className={`${styles.ribbon} ${styles.techRibbon} ${theme === 'dark' ? styles.dark : styles.light}`}>
      <div className={`${styles.ribbonContainer} ${isVisible ? styles.animate : ''}`}>
        <div className={styles.ribbonTrack}>
          {technologies.map((tech, index) => (
            <div key={`${tech.name}-${index}`} className={styles.ribbonItem}>
              <div className={styles.logoContainer} title={tech.name}>
                <img src={tech.logo} alt={tech.alt} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TechRibbon;
