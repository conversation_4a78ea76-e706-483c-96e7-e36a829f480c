import { useEffect, useState } from 'react';
import { useTheme } from '../../hooks/useTheme';
import styles from './RibbonStyles.module.css';

// Import actual tool logos
import photoshopLogo from '../../assets/images/tools-logos/photoshop.svg';
import illustratorLogo from '../../assets/images/tools-logos/illustrator.svg';
import afterEffectsLogo from '../../assets/images/tools-logos/aftereffects.svg';
import premiereProLogo from '../../assets/images/tools-logos/premierepro.svg';
import blenderLogo from '../../assets/images/tools-logos/blender.svg';
import cinema4dLogo from '../../assets/images/tools-logos/cinema4d.svg';
import davinciLogo from '../../assets/images/tools-logos/davinci.svg';
import figmaLogo from '../../assets/images/tools-logos/figma.svg';
import sketchLogo from '../../assets/images/tools-logos/sketch.svg';

/**
 * Tools<PERSON>ibbon component displays a continuously scrolling ribbon of digital content creation tools
 * with grayscale to color hover effect, scrolling in the opposite direction of TechRibbon
 */
const ToolsRibbon = () => {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = useState(false);

  // Digital content creation tools with logos, names, and SEO-optimized alt text
  const toolItems = [
    { name: 'Photoshop', logo: photoshopLogo, alt: 'Adobe Photoshop logo - Professional photo editing and graphic design software used by Hrishikesh Mohite for digital content creation' },
    { name: 'Illustrator', logo: illustratorLogo, alt: 'Adobe Illustrator logo - Vector graphics design software for creating logos, icons, and illustrations' },
    { name: 'After Effects', logo: afterEffectsLogo, alt: 'Adobe After Effects logo - Motion graphics and visual effects software for video production and animation' },
    { name: 'Premiere Pro', logo: premiereProLogo, alt: 'Adobe Premiere Pro logo - Professional video editing software for film and content creation' },
    { name: 'Blender', logo: blenderLogo, alt: 'Blender 3D software logo - Open-source 3D modeling, animation, and rendering tool for creative projects' },
    { name: 'Cinema 4D', logo: cinema4dLogo, alt: 'Cinema 4D logo - Professional 3D modeling, animation, and motion graphics software' },
    { name: 'DaVinci Resolve', logo: davinciLogo, alt: 'DaVinci Resolve logo - Professional video editing, color correction, and post-production software' },
    { name: 'Figma', logo: figmaLogo, alt: 'Figma design tool logo - Collaborative interface design and prototyping platform for UI/UX design' },
    { name: 'Sketch', logo: sketchLogo, alt: 'Sketch design tool logo - Vector-based design tool for user interface and user experience design' },
  ];

  // Create a duplicated array for seamless looping
  // We need to duplicate the entire array to ensure smooth transitions
  const tools = [...toolItems, ...toolItems, ...toolItems];

  // Set animation to always be visible
  useEffect(() => {
    // Immediately set to visible without waiting for intersection
    setIsVisible(true);
  }, []);

  return (
    <div className={`${styles.ribbon} ${styles.toolsRibbon} ${theme === 'dark' ? styles.dark : styles.light}`}>
      <div className={`${styles.ribbonContainer} ${isVisible ? styles.animateReverse : ''}`}>
        <div className={styles.ribbonTrack}>
          {tools.map((tool, index) => (
            <div key={`${tool.name}-${index}`} className={styles.ribbonItem}>
              <div className={styles.logoContainer} title={tool.name}>
                <img src={tool.logo} alt={tool.alt} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ToolsRibbon;
