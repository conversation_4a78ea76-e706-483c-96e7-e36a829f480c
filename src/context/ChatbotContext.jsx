import { useState, useCallback } from 'react';
import { sendMessage } from '../services/chatbotService';
import { ChatbotContext, WELCOME_MESSAGE } from './chatbotConstants';

/**
 * ChatbotProvider component to wrap the application with chatbot functionality
 */
export const ChatbotProvider = ({ children }) => {
  // State for chatbot visibility
  const [isOpen, setIsOpen] = useState(false);

  // State for chat messages
  const [messages, setMessages] = useState([WELCOME_MESSAGE]);

  // State for loading status
  const [isLoading, setIsLoading] = useState(false);

  // State to track if the chatbot has been opened before
  const [hasBeenOpened, setHasBeenOpened] = useState(false);

  // State for input value
  const [inputValue, setInputValue] = useState('');

  // Toggle chatbot visibility
  const toggleChatbot = useCallback(() => {
    setIsOpen(prev => {
      // If opening the chatbot, keep current messages
      // If closing the chatbot, reset the conversation
      if (prev === false) {
        if (!hasBeenOpened) {
          setHasBeenOpened(true);
        }
        return true;
      } else {
        // Reset conversation when closing
        setMessages([WELCOME_MESSAGE]);
        return false;
      }
    });
  }, [hasBeenOpened]);

  // Close chatbot and reset conversation
  const closeChatbot = useCallback(() => {
    setIsOpen(false);
    // Reset conversation when closing
    setMessages([WELCOME_MESSAGE]);
  }, []);

  // Handle input change
  const handleInputChange = useCallback((e) => {
    setInputValue(e.target.value);
  }, []);

  // Handle message submission
  const handleSubmit = useCallback(async (e) => {
    e?.preventDefault();

    if (!inputValue.trim()) return;

    // Add user message to chat
    const userMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      text: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Get response from chatbot service
      const response = await sendMessage(
        userMessage.text,
        messages.map(m => ({ role: m.type === 'user' ? 'user' : 'assistant', content: m.text }))
      );

      // Add bot response to chat
      const botMessage = {
        id: `bot-${Date.now()}`,
        type: 'bot',
        text: response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message to chat
      const errorMessage = {
        id: `error-${Date.now()}`,
        type: 'bot',
        text: "I'm sorry, I encountered an error. Please try again later.",
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [inputValue, messages]);

  // Reset chat
  const resetChat = useCallback(() => {
    setMessages([WELCOME_MESSAGE]);
  }, []);

  // Context value
  const value = {
    isOpen,
    messages,
    isLoading,
    inputValue,
    toggleChatbot,
    closeChatbot,
    handleInputChange,
    handleSubmit,
    resetChat
  };

  return (
    <ChatbotContext.Provider value={value}>
      {children}
    </ChatbotContext.Provider>
  );
};

// The useChatbot hook is now imported from './useChatbot'
