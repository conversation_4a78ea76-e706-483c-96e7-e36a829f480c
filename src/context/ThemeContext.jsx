import { useState, useEffect } from 'react';
import {
  getCookie,
  setCookie,
  hasConsentForCategory,
  COOKIE_CATEGORIES
} from '../utils/cookieManager';
import { ThemeContext } from './themeConstants';

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState(() => {
    // First try to get theme from cookie
    const cookieTheme = getCookie('theme');

    // If cookie exists and user has consented to preference cookies, use it
    if (cookieTheme && hasConsentForCategory(COOKIE_CATEGORIES.PREFERENCES)) {
      return cookieTheme;
    }

    // Otherwise, try localStorage as fallback (for backward compatibility)
    const savedTheme = localStorage.getItem('theme');

    // Default to dark theme if nothing is found
    return savedTheme || 'dark';
  });

  const toggleTheme = () => {
    setTheme(prevTheme => {
      const newTheme = prevTheme === 'light' ? 'dark' : 'light';

      // Store theme in cookie if user has consented to preference cookies
      if (hasConsentForCategory(COOKIE_CATEGORIES.PREFERENCES)) {
        setCookie('theme', newTheme, {
          maxAge: 365 * 24 * 60 * 60, // 1 year
          category: COOKIE_CATEGORIES.PREFERENCES
        });
      }

      // Also store in localStorage for backward compatibility
      localStorage.setItem('theme', newTheme);

      return newTheme;
    });
  };

  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};
