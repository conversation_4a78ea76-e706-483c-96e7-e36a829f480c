:root {
  /* Typography */
  --font-primary: 'Pop<PERSON><PERSON>', sans-serif;
  --font-secondary: '<PERSON><PERSON><PERSON>', sans-serif;

  /* Font Sizes */
  --fs-xs: 0.75rem;    /* 12px */
  --fs-sm: 0.875rem;   /* 14px */
  --fs-base: 1rem;     /* 16px */
  --fs-md: 1.125rem;   /* 18px */
  --fs-lg: 1.25rem;    /* 20px */
  --fs-xl: 1.5rem;     /* 24px */
  --fs-2xl: 2rem;      /* 32px */
  --fs-3xl: 2.5rem;    /* 40px */
  --fs-4xl: 3rem;      /* 48px */
  --fs-5xl: 4rem;      /* 64px */

  /* Font Weights */
  --fw-light: 300;
  --fw-regular: 400;
  --fw-medium: 500;
  --fw-semibold: 600;
  --fw-bold: 700;

  /* Spacing */
  --spacing-xs: 0.25rem;  /* 4px */
  --spacing-sm: 0.5rem;   /* 8px */
  --spacing-md: 1rem;     /* 16px */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */
  --spacing-2xl: 3rem;    /* 48px */
  --spacing-3xl: 4rem;    /* 64px */

  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.5rem;    /* 8px */
  --radius-lg: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Z-index */
  --z-negative: -1;
  --z-normal: 1;
  --z-tooltip: 10;
  --z-fixed: 100;
  --z-modal: 1000;

  /* Box Shadow */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.1);

  /* Container Width */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;

  /* Default Theme: Light */
  --color-primary: #0070f3;  /* Electric Blue */
  --color-primary-rgb: 0, 112, 243;  /* RGB values for rgba() */
  --color-primary-light: #3291ff;
  --color-primary-dark: #0050b3;

  --color-secondary: #ff9500;  /* Vibrant Orange */
  --color-secondary-rgb: 255, 149, 0;  /* RGB values for rgba() */
  --color-secondary-light: #ffb74d;
  --color-secondary-dark: #e67700;

  --color-accent: #ffd700;  /* Gold */

  --color-text: #333333;
  --color-text-light: #666666;
  --color-text-lighter: #999999;

  --color-background: #ffffff;
  --color-background-alt: #f5f5f5;
  --color-background-dark: #e0e0e0;

  --color-border: #e0e0e0;
  --color-border-light: #f0f0f0;

  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Dark Theme */
[data-theme="dark"] {
  --color-primary: #3291ff;
  --color-primary-rgb: 50, 145, 255;  /* RGB values for rgba() */
  --color-primary-light: #5dabff;
  --color-primary-dark: #0050b3;

  --color-secondary: #ff9500;
  --color-secondary-rgb: 255, 149, 0;  /* RGB values for rgba() */
  --color-secondary-light: #ffb74d;
  --color-secondary-dark: #e67700;

  --color-accent: #ffd700;

  --color-text: #f5f5f5;
  --color-text-light: #d4d4d4;
  --color-text-lighter: #a3a3a3;

  --color-background: #121212;
  --color-background-alt: #1e1e1e;
  --color-background-dark: #2d2d2d;

  --color-border: #2d2d2d;
  --color-border-light: #3d3d3d;

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* Reset */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  background-color: var(--color-background);
  color: var(--color-text);
  line-height: 1.6;
  min-height: 100vh;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: var(--fw-bold);
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--fs-4xl);
}

h2 {
  font-size: var(--fs-3xl);
}

h3 {
  font-size: var(--fs-2xl);
}

h4 {
  font-size: var(--fs-xl);
}

h5 {
  font-size: var(--fs-lg);
}

h6 {
  font-size: var(--fs-md);
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-light);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button, .button {
  cursor: pointer;
  font-family: var(--font-primary);
  font-weight: var(--fw-medium);
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--color-primary);
  color: white;
  transition: background-color var(--transition-fast), transform var(--transition-fast);
}

button:hover, .button:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-2px);
}

button:focus, .button:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Container */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap-md {
  gap: var(--spacing-md);
}

.gap-lg {
  gap: var(--spacing-lg);
}

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

/* Responsive Utilities */
@media (max-width: 768px) {
  h1 { font-size: var(--fs-3xl); }
  h2 { font-size: var(--fs-2xl); }
  h3 { font-size: var(--fs-xl); }

  .container {
    padding: 0 var(--spacing-sm);
  }
}
