import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { ThemeProvider } from './context/ThemeContext'
import { ChatbotProvider } from './context/ChatbotContext'
import emailjs from '@emailjs/browser'
import { EMAIL<PERSON>S_CONFIG } from './config/emailjs'
import './index.css'
import App from './App.jsx'

// Initialize EmailJS
emailjs.init(EMAILJS_CONFIG.publicKey)

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <ThemeProvider>
        <ChatbotProvider>
          <App />
        </ChatbotProvider>
      </ThemeProvider>
    </BrowserRouter>
  </StrictMode>,
)
