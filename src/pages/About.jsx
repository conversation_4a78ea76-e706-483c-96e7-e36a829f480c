import { FiCode, FiCpu, FiBriefcase, FiPenTool, FiVideo, FiLayout, FiBook } from 'react-icons/fi';
import Section from '../components/ui/Section';
import Button from '../components/ui/Button';
import styles from './About.module.css';

// Import local images
import profileImage from '../assets/images/hero/optimized/<PERSON><PERSON><PERSON>kesh-Mohite-professional-portrait.png';

const About = () => {
  const skills = [
    { name: 'AI Development', level: 95, icon: <FiCpu /> },
    { name: 'Full-Stack Development', level: 90, icon: <FiCode /> },
    { name: 'Business Strategy', level: 85, icon: <FiBriefcase /> },
    { name: 'Tech Training & Mentorship', level: 88, icon: <FiBook /> },
    { name: 'Graphic Design', level: 80, icon: <FiPenTool /> },
    { name: 'Video Production', level: 75, icon: <FiVideo /> },
    { name: 'Web & UI/UX Design', level: 85, icon: <FiLayout /> }
  ];

  const milestones = [
    {
      year: '2025',
      title: 'Global Reach & Thought Leadership',
      description: 'Expanded operations internationally, establishing partnerships across multiple continents. Published influential research papers and became a recognized thought leader in AI integration and digital transformation strategies for businesses of all sizes.'
    },
    {
      year: '2024',
      title: 'Strategic Expansion & AI Innovation',
      description: 'Expanded business operations with a focus on enterprise AI solutions. Developed proprietary AI frameworks that helped clients achieve significant operational efficiencies and launched an innovation lab dedicated to exploring emerging technologies.'
    },
    {
      year: '2023',
      title: 'Founded Ajinkya Creatiion Pvt. Ltd.',
      description: 'Established Ajinkya Creatiion Pvt. Ltd., a venture dedicated to exploring deep technologies, large language models (LLMs), content creation, and artificial intelligence.'
    },
    {
      year: '2021',
      title: 'Role at Technoedge Learning Solutions',
      description: 'Joined Technoedge Learning Solutions, where I honed my skills in a corporate environment. This period was instrumental in refining my technical abilities and understanding the dynamics of corporate operations.'
    },
    {
      year: '2019',
      title: 'Internship at Reliance Infrastructure',
      description: 'Embarked on my professional journey with an internship at Reliance Infrastructure, focusing on computer graphics. This experience laid the foundation for my passion in visual storytelling and digital design.'
    }
  ];

  return (
    <div className={styles.about}>
      {/* Hero Section */}
      <Section
        id="about-hero"
        padding="large"
      >
        <div className={styles.aboutHero}>
          <div className={styles.aboutImage}>
            <img
              src={profileImage}
              alt="Hrishikesh Mohite - Professional Portrait of AI Developer, Full-Stack Expert & Tech Mentor"
              className={styles.profilePhoto}
              style={{
                objectFit: 'cover',
                imageRendering: 'auto',
                colorRendering: 'optimizeQuality',
                colorInterpolation: 'linearRGB',
                colorAdjust: 'exact',
                filter: 'none',
                WebkitFilter: 'none'
              }}
            />
            <div className={styles.aboutImageOverlay}>
              <div className={styles.aboutImageInfo}>
                <h3>Hrishikesh Mohite</h3>
                <p>Entrepreneur & Tech Mentor</p>
              </div>
            </div>
          </div>
          <div className={styles.aboutContent}>
            <h1>Hrishikesh Mohite: AI Strategist & Tech Innovator</h1>
            <h2>Entrepreneur, AI Strategist, Full-Stack Developer & Tech Mentorship Expert</h2>
            <p>
            I’m Hrishikesh Mohite, a visionary tech innovator and AI strategist specializing in business transformation through cutting-edge technology solutions.
            As a full-stack developer and passionate tech mentorship advocate, I bridge the gap between complex AI technologies and practical business applications.
            My expertise spans AI in education, custom software development, and strategic technology consulting, helping organizations harness the power of artificial intelligence for measurable growth.
            From writing books to designing visual experiences and architecting powerful applications, I’ve spent years mastering multiple disciplines to shape ideas into high-impact realities.
            </p>
            <div className={styles.aboutStats}>
              <div className={styles.statItem}>
                <span className={styles.statNumber}>10+</span>
                <span className={styles.statLabel}>Years Experience</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statNumber}>100+</span>
                <span className={styles.statLabel}>Projects Completed</span>
              </div>
              <div className={styles.statItem}>
                <span className={styles.statNumber}>50+</span>
                <span className={styles.statLabel}>Happy Clients</span>
              </div>
            </div>
          </div>
        </div>
      </Section>

      {/* Journey Section */}
      <Section
        id="journey"
        title="My Journey"
        subtitle="The path that led me to AI development and full-stack expertise"
        background="light"
      >
        <div className={styles.journey}>
          <p>
            My journey began with a single spark — a passion for visual storytelling. I started out as a graphic designer, driven by a curiosity to bring imagination to life through visuals. That passion quickly expanded as I taught myself video editing, 3D modeling, and a wide range of digital content creation tools. In just two years, I transformed from a designer into a multidisciplinary creator capable of producing any form of digital media.
          </p>
          <p>
            But I wasn't done.
          </p>
          <p>
            With a growing fascination for how things work behind the scenes, I dove headfirst into the world of technology and programming. I began learning modern coding languages and frameworks — from <strong>Kotlin</strong> for Android apps, to <strong>JavaScript</strong>, <strong>TypeScript</strong>, <strong>React</strong>, Vue, Vite, Electron, Astro, and cutting-edge AI platforms like <strong>Gemini Google</strong>. Each new language and technology wasn't just a skill — it was a new creative weapon for building custom applications and intelligent digital solutions.
          </p>
          <p>
            Fueled by passion and persistence, I built my first fully functional application using <strong>React</strong> and <strong>Node.js</strong>. That moment wasn't just an achievement — it was a turning point. It marked the beginning of something much bigger: a lifelong pursuit to create meaningful, intelligent, and beautifully designed digital products that drive business innovation.
          </p>
          <p>
            That's when I founded <a href="https://ajinkyacreatiion.com" target="_blank" rel="noopener noreferrer" className={styles.ajinkyaLink}>Ajinkya Creatiion</a> — not just a company, but a vision. A place where art meets engineering, where strategy drives execution, and where we build bold, innovative solutions for businesses, creators, and visionaries through <strong>AI development</strong> and <strong>full-stack expertise</strong>.
          </p>
          <p>
            Today, I stand not only as a developer or designer — but as an entrepreneur, business strategist, AI innovator, and <strong>tech mentor</strong>. My goal is simple but powerful: to merge creativity with technology to shape the future and empower others — one idea, one product, one solution, one student at a time.
          </p>
        </div>
      </Section>

      {/* Skills Section */}
      <Section
        id="skills"
        title="Technical Expertise"
        subtitle="Specialized skills across AI development, full-stack solutions & tech mentorship"
      >
        <div className={styles.skillsGrid}>
          {skills.map((skill, index) => (
            <div key={index} className={styles.skillCard}>
              <div className={styles.skillIcon}>{skill.icon}</div>
              <div className={styles.skillInfo}>
                <h3>{skill.name}</h3>
                <div className={styles.skillBar}>
                  <div
                    className={styles.skillProgress}
                    style={{ width: `${skill.level}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Vision Section */}
      <Section
        id="vision"
        title="My Vision"
        subtitle="Driving business innovation through technology and creative strategy"
        background="light"
      >
        <div className={styles.vision}>
          <p>
            I believe the future belongs to those who can imagine it — and then build it with the right technology stack.
          </p>
          <p>
            My vision is to be a driving force behind the business transformation of tomorrow's world. As an AI strategist and tech innovator, I see a future where innovation isn't just about writing better code or designing better visuals — it's about building smarter, more human-centered solutions that solve real problems and inspire real change through strategic business transformation.
          </p>
          <p>
            I want to lead that future through <strong>AI development</strong> and <strong>tech mentorship</strong>.
          </p>
          <p>
            Through <a href="https://ajinkyacreatiion.com" target="_blank" rel="noopener noreferrer" className={styles.ajinkyaLink}>Ajinkya Creatiion</a>, my mission is to help individuals, startups, and enterprises not only keep up with change — but lead it. I envision a world where brands are more intelligent, experiences more immersive, and technology more accessible. A world where design isn't just seen, but felt — and where AI doesn't replace people, but amplifies their vision.
          </p>
          <p>
            Every project I take on, every line of <strong>React</strong> or <strong>Node.js</strong> code I write, and every design I create is part of a bigger purpose:
            To empower creators. To build with intention. To leave a legacy of innovation that speaks louder than words.
          </p>
          <p>
            This isn't just my career.
            This is my calling.
          </p>
        </div>
      </Section>

      {/* Milestones Section */}
      <Section
        id="milestones"
        title="Career Milestones"
        subtitle="Key steps along my professional journey"
      >
        <div className={styles.timeline}>
          {milestones.map((milestone, index) => (
            <div key={index} className={styles.timelineItem}>
              <div className={styles.timelineYear}>{milestone.year}</div>
              <div className={styles.timelineContent}>
                <h3>{milestone.title}</h3>
                <p>{milestone.description}</p>
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Author Section */}
      <Section
        id="author"
        title="Author & Mentor"
        subtitle="Sharing Knowledge Through Books and Mentorship Programs"
        background="light"
      >
        <div className={styles.author}>
          <p>
            Beyond my work as an AI strategist, full-stack developer, and tech innovator, I'm a passionate author and tech mentor dedicated to turning complex ideas into powerful narratives and practical skills that drive action. My writing and teaching focus heavily on AI in education, helping educators and students harness the power of technology for enhanced learning experiences. My work isn't just about knowledge — it's about transformation.
          </p>
          <p>
            Each book I write and mentorship program I develop is a reflection of my journey through technology, innovation, and human experience. I explore the intersection of entrepreneurship, leadership, and the evolving digital landscape, offering readers and students not just inspiration — but real-world tools to navigate, lead, and build in an ever-changing world.
          </p>
          <p>
            My mission as an author and <strong>tech mentor</strong> is simple:
            To empower thinkers, makers, and dreamers with ideas and skills that challenge convention, spark innovation, and unlock potential. Whether it's implementing AI in education solutions, teaching <strong>React</strong> and <strong>Node.js</strong> development, or guiding business transformation through technology, my tech mentorship approach is designed to move people — and move them forward.
          </p>
          <p>
            Writing and teaching aren't just passions — <span className={styles.legacyText}>they're part of the legacy I'm building.</span>
          </p>
          <div className={styles.authorCta}>
            <Button to="/books" variant="primary">
              Explore My Books
            </Button>
            <Button to="/services#tech-trainer" variant="outline">
              Tech Mentorship Programs
            </Button>
          </div>
        </div>
      </Section>

      {/* CTA Section */}
      <Section
        id="cta"
        padding="large"
        background="none"
        className={styles.ctaSection}
      >
        <div className={styles.cta}>
          <h2>Let's Drive Your Business Transformation Together</h2>
          <p>
            Ready to leverage AI in education, custom AI solutions, full-stack development, or tech mentorship for your project?
            As an AI strategist and tech innovator, I'd love to hear from you and discuss how we can achieve your business transformation goals.
          </p>
          <Button to="/contact" variant="primary" size="large">
            Get in Touch
          </Button>
        </div>
      </Section>
    </div>
  );
};

export default About;
