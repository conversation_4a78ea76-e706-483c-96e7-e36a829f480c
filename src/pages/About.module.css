.about {
  width: 100%;
}

/* About Hero Section */
.aboutHero {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.aboutImage {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.aboutImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Further refined gradient to preserve natural colors, especially around lips */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1) 60%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
  /* Ensure we're not affecting color saturation */
  mix-blend-mode: normal;
  pointer-events: none;
}

.aboutImage img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform var(--transition-normal);
  /* Ensure color fidelity is preserved */
  filter: none;
  -webkit-filter: none;
  /* Prevent any color shifts */
  color-rendering: optimizeQuality;
  image-rendering: optimizeQuality;
}

/* Enhanced styles for profile photo to maintain color fidelity */
.profilePhoto {
  filter: none !important;
  -webkit-filter: none !important;
  image-rendering: auto !important;
  color-rendering: optimizeQuality !important;
  color-interpolation: linearRGB !important;
  color-adjust: exact !important;
  color-space: srgb !important;
  /* Prevent any browser-specific color adjustments */
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important;
}

.aboutImage:hover {
  box-shadow: var(--shadow-xl);
}

.aboutImage:hover::before {
  opacity: 1;
}

.aboutImage:hover img {
  transform: scale(1.03);
  /* Ensure no color distortion on hover */
  filter: none;
  -webkit-filter: none;
}

.aboutImageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-md);
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--transition-normal), transform var(--transition-normal);
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  /* Ensure overlay doesn't affect image colors */
  pointer-events: none;
  mix-blend-mode: normal;
}

.aboutImageInfo {
  text-align: left;
  color: white;
  padding-left: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
}

.aboutImageInfo h3 {
  font-size: var(--fs-xl);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--fw-bold);
}

.aboutImageInfo p {
  font-size: var(--fs-md);
  opacity: 0.9;
}

.aboutImage:hover .aboutImageOverlay {
  opacity: 1;
  transform: translateY(0);
}

.aboutContent h1 {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-md);
}

.aboutContent h2 {
  font-size: var(--fs-xl);
  font-weight: var(--fw-medium);
  color: var(--color-primary);
  margin-top: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
}

.aboutContent p {
  font-size: var(--fs-lg);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.ajinkyaLink {
  color: var(--color-primary);
  font-weight: var(--fw-medium);
  text-decoration: none;
  position: relative;
  transition: color var(--transition-normal);
}

.ajinkyaLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.ajinkyaLink:hover {
  color: var(--color-primary-light);
}

.ajinkyaLink:hover::after {
  width: 100%;
}

.aboutStats {
  display: flex;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.statNumber {
  font-size: var(--fs-3xl);
  font-weight: var(--fw-bold);
  color: var(--color-primary);
}

.statLabel {
  font-size: var(--fs-sm);
  color: var(--color-text-light);
  margin-top: var(--spacing-xs);
}

/* Journey Section */
.journey {
  font-size: var(--fs-md);
  line-height: 1.8;
}

.journey p {
  margin-bottom: var(--spacing-lg);
}

/* Skills Section */
.skillsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xl);
}

.skillCard {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.skillCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.skillIcon {
  font-size: var(--fs-2xl);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
}

.skillInfo {
  flex: 1;
}

.skillInfo h3 {
  font-size: var(--fs-md);
  margin-bottom: var(--spacing-sm);
}

.skillBar {
  height: 8px;
  background-color: var(--color-background-dark);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.skillProgress {
  height: 100%;
  background-color: var(--color-primary);
  border-radius: var(--radius-full);
}

/* Vision Section */
.vision {
  font-size: var(--fs-md);
  line-height: 1.8;
}

.vision p {
  margin-bottom: var(--spacing-lg);
}

/* Timeline Section */
.timeline {
  position: relative;
  padding-left: 50px;
}

.timeline::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20px;
  height: 100%;
  width: 2px;
  background-color: var(--color-primary);
}

.timelineItem {
  position: relative;
  margin-bottom: var(--spacing-xl);
}

.timelineItem:last-child {
  margin-bottom: 0;
}

.timelineYear {
  position: absolute;
  left: -50px;
  top: 0;
  width: 40px;
  height: 40px;
  background-color: var(--color-primary);
  color: white;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--fw-bold);
  z-index: 1;
}

/* Spinning Glow Animation */
@keyframes spinGlow {
  0% {
    background-position: 0% 0%;
    border-image: linear-gradient(
      0deg,
      var(--color-primary-light),
      var(--color-secondary),
      var(--color-primary-light)
    ) 1;
  }
  25% {
    background-position: 100% 0%;
    border-image: linear-gradient(
      90deg,
      var(--color-primary-light),
      var(--color-secondary),
      var(--color-primary-light)
    ) 1;
  }
  50% {
    background-position: 100% 100%;
    border-image: linear-gradient(
      180deg,
      var(--color-primary-light),
      var(--color-secondary),
      var(--color-primary-light)
    ) 1;
  }
  75% {
    background-position: 0% 100%;
    border-image: linear-gradient(
      270deg,
      var(--color-primary-light),
      var(--color-secondary),
      var(--color-primary-light)
    ) 1;
  }
  100% {
    background-position: 0% 0%;
    border-image: linear-gradient(
      360deg,
      var(--color-primary-light),
      var(--color-secondary),
      var(--color-primary-light)
    ) 1;
  }
}

.timelineContent {
  position: relative;
  padding: var(--spacing-lg);
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  border: 3px solid transparent;
  background-clip: padding-box;
  z-index: 1;
  will-change: transform, box-shadow, border-image;
}

.timelineContent::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: var(--color-background);
  border-radius: var(--radius-lg);
  z-index: -1;
}

.timelineContent:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(var(--color-primary-rgb), 0.2),
              0 6px 6px rgba(var(--color-secondary-rgb), 0.1),
              0 0 8px rgba(var(--color-primary-rgb), 0.3);
  border-image: linear-gradient(
    45deg,
    var(--color-primary-light),
    var(--color-secondary),
    var(--color-primary-light)
  ) 1;
  animation: spinGlow 4s ease-in-out infinite;
}

.timelineContent:hover::before {
  background: var(--color-background);
}

.timelineContent h3 {
  margin-bottom: var(--spacing-sm);
  transition: color var(--transition-normal);
}

.timelineContent:hover h3 {
  color: var(--color-primary);
  text-shadow: 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.timelineContent p {
  transition: color var(--transition-normal);
}

.timelineContent:hover p {
  color: var(--color-text);
}

/* Author Section */
.author {
  font-size: var(--fs-md);
  line-height: 1.8;
}

.author p {
  margin-bottom: var(--spacing-lg);
}

.legacyText {
  font-weight: var(--fw-bold);
  position: relative;
  transition: color var(--transition-normal);
  cursor: pointer;
}

.legacyText::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #c41e3a; /* Royal Blood red color */
  transition: width var(--transition-normal);
}

.legacyText:hover {
  color: #c41e3a; /* Royal Blood red color */
}

.legacyText:hover::after {
  width: 100%;
}

.authorCta {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

/* CTA Section */
.ctaSection {
  background-color: var(--color-background);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.ctaSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.cta h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.cta p {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-light);
}

.cta button {
  position: relative;
  overflow: hidden;
  font-weight: var(--fw-bold);
  letter-spacing: 0.5px;
  padding: calc(var(--spacing-md) + 2px) calc(var(--spacing-xl) + 5px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.cta button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .aboutHero {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .aboutImage {
    max-width: 400px;
    margin: 0 auto;
  }

  .skillsGrid {
    grid-template-columns: 1fr;
  }

  /* Adjust timeline for medium screens */
  .timelineContent {
    border-width: 2px;
  }

  .timelineContent::before {
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
  }
}

@media (max-width: 768px) {
  .aboutStats {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .authorCta {
    flex-direction: column;
  }

  .aboutImageInfo {
    padding-left: var(--spacing-sm);
  }

  .aboutImageInfo h3 {
    font-size: var(--fs-lg);
  }

  .aboutImageInfo p {
    font-size: var(--fs-sm);
  }

  /* Adjust timeline for small screens */
  .timeline {
    padding-left: 40px;
  }

  .timeline::before {
    left: 15px;
  }

  .timelineYear {
    left: -40px;
    width: 30px;
    height: 30px;
    font-size: var(--fs-sm);
  }

  .timelineContent {
    padding: var(--spacing-md);
  }

  /* Optimize animation for mobile devices */
  @media (prefers-reduced-motion: reduce) {
    .timelineContent:hover {
      animation: none;
      border-image: linear-gradient(
        45deg,
        var(--color-primary-light),
        var(--color-secondary),
        var(--color-primary-light)
      ) 1;
    }
  }
}
