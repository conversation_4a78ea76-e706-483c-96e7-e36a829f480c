import { <PERSON>B<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/fi';
import Section from '../components/ui/Section';
import Button from '../components/ui/Button';
import NetworkBackground from '../components/ui/NetworkBackground';
import styles from './Books.module.css';

// Import local book cover images
import entrepreneurMindset from '../assets/images/books/Hrishikesh-Mohite-entrepreneur-mindset-book.jpg';
import symphonyOfStars from '../assets/images/books/Hrishikesh-Mohite-symphony-of-stars-book.jpg';
import loveThroughAdversity from '../assets/images/books/Hrishikesh-Mohite-love-through-adversity-book.jpg';
import controlPride from '../assets/images/books/Hrishikesh-Mohite-control-pride-book.jpg';
import timeToShow from '../assets/images/books/Hrishikesh-Mohite-time-to-show-book.jpg';

// Book covers object
const bookCovers = {
  entrepreneurMindset,
  symphonyOfStars,
  loveThroughAdversity,
  controlPride,
  timeToShow
};

const Books = () => {
  const books = [
    {
      id: 1,
      title: 'Entrepreneur Mindset: Leadership, Success, and Motivation for High Achievers',
      cover: bookCovers.entrepreneurMindset,
      altText: 'Entrepreneur Mindset book cover by Hrishikesh Mohite - Leadership and business success guide for high achievers and entrepreneurs',
      description: 'Unlock the Entrepreneur Mindset! Learn proven strategies to build, lead, and scale your business while staying motivated and avoiding burnout. This book is your blueprint for success, leadership, and lasting impact in the competitive business world.',
      publishDate: 'March 30, 2025',
      amazonLink: 'https://www.amazon.com/Entrepreneur-Mindset-Leadership-Motivation-Strategies-ebook/dp/B0F329D2V8/',
      highlights: [
        'Proven strategies for business leadership',
        'Techniques to stay motivated and avoid burnout',
        'Methods for scaling your business effectively',
        'Frameworks for success in competitive markets'
      ]
    },
    {
      id: 2,
      title: 'A Symphony of Stars: A Tale of Love and Resilience',
      cover: bookCovers.symphonyOfStars,
      altText: 'A Symphony of Stars book cover by Hrishikesh Mohite - Romance novel about love and resilience set in Mumbai',
      description: 'In the heart of bustling Mumbai, two souls from vastly different worlds collide in a tale that transcends the boundaries of time and circumstance. A poignant and heartwarming story that explores the depth of human emotions and the enduring power of love.',
      publishDate: 'July 11, 2024',
      amazonLink: 'https://www.amazon.com/Symphony-Stars-Tale-Love-Resilience-ebook/dp/B0D9CDBHX1/',
      highlights: [
        'Emotional journey of love and self-discovery',
        'Exploration of resilience in relationships',
        'Vivid portrayal of Mumbai\'s vibrant culture',
        'Themes of overcoming societal expectations'
      ]
    },
    {
      id: 3,
      title: 'Love Through Adversity: Overcoming Challenges Together',
      cover: bookCovers.loveThroughAdversity,
      altText: 'Love Through Adversity book cover by Hrishikesh Mohite - Inspirational romance about overcoming challenges and personal growth',
      description: 'In the heart of a bustling city, two strangers\' lives intertwine in unexpected ways. Alex and Mia discover that true strength lies not in never falling but in rising every time they do. A heartwarming tale that explores the depths of human connection and the power of unwavering support.',
      publishDate: 'July 7, 2024',
      amazonLink: 'https://www.amazon.com/Love-Through-Adversity-Overcoming-Challenges-ebook/dp/B0CY27FCCM/',
      highlights: [
        'Journey of resilience and personal growth',
        'Exploration of love as a supportive force',
        'Strategies for overcoming life\'s challenges together',
        'Inspirational story of determination and hope'
      ]
    }
  ];

  return (
    <div className={styles.books}>
      {/* Hero Section */}
      <Section
        id="books-hero"
        padding="large"
        background="light"
        className={styles.heroSection}
      >
        <div className={styles.networkBackgroundContainer}>
          <NetworkBackground
            color="#10b981"
            shape="books"
            pointCount={80}
            connectionDistance={175}
            baseOpacity={0.4}
            highlightOpacity={0.9}
          />
        </div>
        <div className={styles.booksHero}>
          <h1>Books by Hrishikesh Mohite | Technology & Leadership</h1>
          <p>
            Explore my published works spanning entrepreneurship, motivation, and heartfelt fiction.
            My books offer a blend of practical business insights and compelling narratives that
            inspire readers to overcome challenges and pursue their dreams with passion and resilience.
          </p>
          <Button
            href="https://www.amazon.com/s?i=stripbooks&rh=p_27%3AHrishikesh%2BMohite&s=date-desc-rank&qid=1747580950&text=Hrishikesh+Mohite&ref=sr_st_date-desc-rank"
            variant="primary"
            target="_blank"
            rel="noopener noreferrer"
          >
            <FiBook /> Visit Amazon Author Page
          </Button>
        </div>
      </Section>

      {/* Books Section */}
      <Section id="published-books">
        <div className={styles.booksGrid}>
          {books.map(book => (
            <div key={book.id} className={styles.bookCard}>
              <div className={styles.bookCover}>
                <img src={book.cover} alt={book.altText} />
              </div>
              <div className={styles.bookDetails}>
                <h2>{book.title}</h2>
                <p className={styles.publishDate}>Published: {book.publishDate}</p>
                <p className={styles.bookDescription}>{book.description}</p>

                <div className={styles.bookHighlights}>
                  <h3><FiAward /> Key Highlights</h3>
                  <ul>
                    {book.highlights.map((highlight, index) => (
                      <li key={index}>{highlight}</li>
                    ))}
                  </ul>
                </div>

                <Button
                  href={book.amazonLink}
                  variant="primary"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <FiBookOpen /> Read on Amazon
                </Button>
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Writing Journey Section */}
      <Section
        id="writing-journey"
        title="My Writing Journey"
        subtitle="From entrepreneur to published author of both fiction and non-fiction"
        background="light"
      >
        <div className={styles.writingJourney}>
          <p>
            My journey as an author began with a passion for sharing ideas that inspire and empower others.
            As an entrepreneur and creative professional, I've always believed in the transformative power of
            storytelling and knowledge sharing. My books span both non-fiction works focused on entrepreneurship
            and leadership, as well as fiction that explores the depth of human emotions and resilience.
          </p>
          <p>
            Writing allows me to connect with readers on a deeper level, offering insights from my experiences
            while also creating narratives that resonate with universal human experiences. Whether I'm crafting
            a guide on entrepreneurial mindset or weaving a tale of love and perseverance, my goal is to create
            work that inspires, educates, and entertains.
          </p>
          <p>
            Each book represents a labor of love and dedication, reflecting my commitment to quality and authenticity.
            I draw inspiration from real-life experiences, conversations, and observations, transforming them into
            stories and frameworks that readers can apply to their own lives. I'm passionate about writing books
            that challenge convention and empower thinkers and makers with ideas that can drive positive change.
          </p>
        </div>
      </Section>

      {/* More Books Section */}
      <Section
        id="more-books"
        title="More Books"
        subtitle="Additional publications to explore"
      >
        <div className={styles.upcomingWorks}>
          <div className={styles.upcomingBook}>
            <div className={styles.upcomingBookCover}>
              <img src={bookCovers.controlPride} alt="Control Pride book cover by Hrishikesh Mohite - Self-improvement guide for taking control of your life and managing pride" />
            </div>
            <div className={styles.upcomingBookDetails}>
              <h3>Control Pride & Take Control of Your Life</h3>
              <p>
                This transformative guide helps you navigate the complexities of pride, offering insights into its
                destructive effects and the paradoxical nature of its strength and weakness. With practical strategies
                for self-control, you'll learn to balance confidence and humility, embracing a life of authenticity and purpose.
              </p>
              <p className={styles.releaseDate}>Published: July 2, 2023</p>
              <Button
                href="https://www.amazon.com/Control-Pride-Take-Your-Life-ebook/dp/B0C9PBXVKP/"
                variant="primary"
                target="_blank"
                rel="noopener noreferrer"
                className={styles.moreBookButton}
              >
                <FiBookOpen /> Read on Amazon
              </Button>
            </div>
          </div>

          <div className={styles.upcomingBook}>
            <div className={styles.upcomingBookCover}>
              <img src={bookCovers.timeToShow} alt="Time to Show Them What You're Made Of book cover by Hrishikesh Mohite - Motivational guide for self-discovery and personal growth" />
            </div>
            <div className={styles.upcomingBookDetails}>
              <h3>Time to Show Them What You're Made Of</h3>
              <p>
                This empowering guide takes you on a transformative journey of self-discovery and personal growth.
                It invites you to embrace your unique talents, strengths, and abilities, and harness them to create
                a life of purpose, fulfillment, and impact. Discover the power of self-discipline and unwavering belief in yourself.
              </p>
              <p className={styles.releaseDate}>Published: June 8, 2023</p>
              <Button
                href="https://www.amazon.com/Time-Show-Them-What-Youre-ebook/dp/B0C7L8XNCB/"
                variant="primary"
                target="_blank"
                rel="noopener noreferrer"
                className={styles.moreBookButton}
              >
                <FiBookOpen /> Read on Amazon
              </Button>
            </div>
          </div>
        </div>
      </Section>

      {/* Knowledge Sharing Section */}
      <Section
        id="knowledge-sharing"
        background="light"
        centered
      >
        <div className={styles.knowledgeSharing}>
          <h2>Tech Mentorship Through Published Knowledge</h2>
          <p>
            My approach to tech mentorship extends beyond traditional teaching methods through comprehensive published works
            that bridge theory and practice. Each book represents years of experience in AI in education, custom AI solutions,
            and full-stack development, distilled into actionable insights for readers.
          </p>
          <p>
            Whether exploring entrepreneurial strategies or diving deep into technology implementation, my books serve as
            mentorship tools that guide readers through complex concepts. From ChatGPT integration strategies to Microsoft
            Copilot optimization techniques, I share practical knowledge that drives real-world results.
          </p>
          <p>
            Through my writing, I aim to democratize access to advanced technology knowledge, making complex AI and
            development concepts accessible to entrepreneurs, students, and professionals seeking to leverage technology
            for growth and innovation in their respective fields.
          </p>
        </div>
      </Section>

      {/* CTA Section */}
      <Section
        id="cta"
        padding="large"
        background="none"
        className={styles.ctaSection}
      >
        <div className={styles.cta}>
          <h2>Interested in my insights?</h2>
          <p>
            Whether you're looking for a speaker for your event, a consultant for your
            organization, or just want to discuss the ideas in my books, I'd love to connect.
          </p>
          <Button to="/contact" variant="primary" size="large">
            Get in Touch
          </Button>
        </div>
      </Section>
    </div>
  );
};

export default Books;
