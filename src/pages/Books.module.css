.books {
  width: 100%;
}

/* Books Hero Section */
.heroSection {
  position: relative;
  overflow: hidden;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.networkBackgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.booksHero {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.booksHero h1 {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-lg);
}

.booksHero p {
  font-size: var(--fs-lg);
  line-height: 1.7;
  margin-bottom: var(--spacing-xl);
}

/* Knowledge Sharing Section */
.knowledgeSharing {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.knowledgeSharing h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-primary);
}

.knowledgeSharing p {
  margin-bottom: var(--spacing-md);
  font-size: var(--fs-lg);
  line-height: 1.7;
  color: var(--color-text-secondary);
  text-align: left;
}

/* Books Grid */
.booksGrid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.bookCard {
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: var(--spacing-xl);
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.bookCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.bookCover {
  height: 570px; /* Adjusted height for better book cover aspect ratio */
  overflow: hidden;
  position: relative;
  background-color: var(--color-background-alt);
  border: 1px solid var(--color-border);
  display: flex;
  align-items: stretch;
  justify-content: center;
}

.bookCover img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top; /* Align to top to ensure no space at bottom */
  transition: transform var(--transition-normal);
  margin: 0;
}

.bookCard:hover .bookCover img {
  transform: scale(1.03);
}

.bookDetails {
  padding: var(--spacing-xl) var(--spacing-2xl);
  display: flex;
  flex-direction: column;
}

.bookDetails h2 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--spacing-xs);
}

.publishDate {
  color: var(--color-text-light);
  font-size: var(--fs-sm);
  margin-bottom: var(--spacing-md);
}

.bookDescription {
  font-size: var(--fs-md);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.bookHighlights {
  margin-bottom: var(--spacing-xl);
}

.bookHighlights h3 {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--color-primary);
}

.bookHighlights ul {
  list-style: none;
  padding: 0;
}

.bookHighlights li {
  position: relative;
  padding-left: 24px;
  margin-bottom: var(--spacing-sm);
  font-size: var(--fs-md);
}

.bookHighlights li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: var(--fw-bold);
}

/* Writing Journey Section */
.writingJourney {
  font-size: var(--fs-md);
  line-height: 1.8;
}

.writingJourney p {
  margin-bottom: var(--spacing-lg);
}

/* More Books Section */
.upcomingWorks {
  padding: var(--spacing-xl);
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border-left: 4px solid var(--color-primary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.upcomingBook {
  display: flex;
  gap: var(--spacing-xl);
}

.moreBookButton {
  align-self: flex-start;
  margin-top: var(--spacing-md);
}

.upcomingBookIcon {
  color: var(--color-primary);
  display: flex;
  align-items: flex-start;
  justify-content: center;
}

.upcomingBookCover {
  width: 150px;
  height: 225px;
  overflow: hidden;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
  position: relative;
  background-color: var(--color-background-alt);
  border: 1px solid var(--color-border);
  display: flex;
  align-items: stretch;
  justify-content: center;
}

.upcomingBookCover img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top; /* Align to top to ensure no space at bottom */
  transition: transform var(--transition-normal);
  margin: 0;
}

.upcomingBook:hover .upcomingBookCover img {
  transform: scale(1.05);
}

.upcomingBookDetails h3 {
  font-size: var(--fs-xl);
  margin-bottom: var(--spacing-md);
}

.upcomingBookDetails p {
  font-size: var(--fs-md);
  line-height: 1.7;
  margin-bottom: var(--spacing-md);
}

.releaseDate {
  font-weight: var(--fw-medium);
  color: var(--color-primary);
}

/* CTA Section */
.ctaSection {
  background-color: var(--color-background);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.ctaSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.cta h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.cta p {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-light);
}

.cta button {
  position: relative;
  overflow: hidden;
  font-weight: var(--fw-bold);
  letter-spacing: 0.5px;
  padding: calc(var(--spacing-md) + 2px) calc(var(--spacing-xl) + 5px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.cta button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .bookCard {
    grid-template-columns: 340px 1fr;
  }
}

@media (max-width: 992px) {
  .bookCard {
    grid-template-columns: 1fr;
  }

  .bookCover {
    height: 500px;
    max-width: 350px;
    margin: 0 auto;
    border-radius: var(--radius-md);
  }

  .bookCover img {
    border-radius: var(--radius-md);
  }
}

@media (max-width: 768px) {
  .booksHero h1 {
    font-size: var(--fs-3xl);
  }

  .bookDetails h2 {
    font-size: var(--fs-xl);
  }

  .upcomingBook {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .upcomingBookCover {
    width: 200px;
    height: 300px;
    margin-bottom: var(--spacing-md);
  }

  /* No need for ctaButtons styles as we've removed the multiple buttons */
}
