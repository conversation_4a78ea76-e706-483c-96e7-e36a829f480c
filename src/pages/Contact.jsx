import { FiMail, FiLinkedin, FiMapPin, FiClock } from 'react-icons/fi';
import Section from '../components/ui/Section';
import ContactForm from '../components/ui/ContactForm';
import NetworkBackground from '../components/ui/NetworkBackground';
import styles from './Contact.module.css';

const Contact = () => {
  return (
    <div className={styles.contact}>
      {/* Hero Section */}
      <Section
        id="contact-hero"
        padding="large"
        background="light"
        className={styles.heroSection}
      >
        <div className={styles.networkBackgroundContainer}>
          <NetworkBackground
            color="#ff9500"
            shape="contact"
            pointCount={80}
            connectionDistance={175}
            baseOpacity={0.4}
            highlightOpacity={0.9}
          />
        </div>
        <div className={styles.contactHero}>
          <h1>Contact <PERSON><PERSON><PERSON><PERSON><PERSON> for AI Consulting</h1>
          <p>
            I'm always interested in hearing about new projects, opportunities,
            and collaborations. Whether you have a question about my services,
            need a consultation, or just want to say hello, I'd love to hear from you.
          </p>
        </div>
      </Section>

      {/* Contact Section */}
      <Section id="contact-details">
        <div className={styles.contactGrid}>
          <div className={styles.contactFormContainer}>
            <h2>Send a Message</h2>
            <p>
              Fill out the form below with your details and I'll get back to you as soon as possible.
            </p>
            <ContactForm />
          </div>

          <div className={styles.contactInfo}>
            <h2>Contact Information</h2>
            <p>
              Here are the best ways to reach me for inquiries and collaborations.
            </p>
            <div className={styles.contactCard}>
              <div className={styles.contactIcon}>
                <FiMail />
              </div>
              <div className={styles.contactText}>
                <h3>Email</h3>
                <p>
                  <a href="mailto:<EMAIL>">
                    <EMAIL>
                  </a>
                </p>
                <p className={styles.contactNote}>
                  For general inquiries and project discussions
                </p>
              </div>
            </div>

            <div className={styles.contactCard}>
              <div className={styles.contactIcon}>
                <FiLinkedin />
              </div>
              <div className={styles.contactText}>
                <h3>LinkedIn</h3>
                <p>
                  <a
                    href="https://linkedin.com/in/hrishikeshmohite"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    linkedin.com/in/hrishikeshmohite
                  </a>
                </p>
                <p className={styles.contactNote}>
                  Connect with me professionally
                </p>
              </div>
            </div>

            <div className={styles.contactCard}>
              <div className={styles.contactIcon}>
                <FiMapPin />
              </div>
              <div className={styles.contactText}>
                <h3>Availability</h3>
                <p>Remote collaboration worldwide</p>
                <p className={styles.contactNote}>
                  Available for virtual meetings and remote project collaboration globally.
                </p>
              </div>
            </div>

            <div className={styles.contactCard}>
              <div className={styles.contactIcon}>
                <FiClock />
              </div>
              <div className={styles.contactText}>
                <h3>Response Time</h3>
                <p>Within 24-48 hours</p>
                <p className={styles.contactNote}>
                  I strive to respond to all inquiries promptly
                </p>
              </div>
            </div>

            <div className={styles.contactNote}>
              <h3>Let's Build the Future, Together</h3>
              <p>
              Let's create something visionary — tech, design, or beyond.
              </p>
            </div>
          </div>
        </div>
      </Section>

      {/* Collaboration Approach Section */}
      <Section
        id="collaboration-approach"
        background="light"
        centered
      >
        <div className={styles.collaborationApproach}>
          <h2>AI Consulting & Full-Stack Development Approach</h2>
          <p>
            My consultation methodology combines deep technical expertise in AI in education and custom AI solutions
            with strategic business insights. Whether you need ChatGPT integration, Microsoft Copilot implementation,
            or Gemini Google optimization, I provide comprehensive guidance tailored to your specific requirements.
          </p>
          <p>
            Through tech mentorship and collaborative development, I ensure that every project not only meets immediate
            needs but also builds long-term capabilities within your organization. My full-stack development approach
            encompasses everything from initial strategy to final implementation and ongoing optimization.
          </p>
          <p>
            From AI in education platforms to enterprise-level custom AI solutions, I work closely with clients to
            understand their unique challenges and deliver measurable results that drive business growth and innovation
            in their respective industries.
          </p>
        </div>
      </Section>

    </div>
  );
};

export default Contact;
