.contact {
  width: 100%;
}

/* Contact Hero Section */
.heroSection {
  position: relative;
  overflow: hidden;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.networkBackgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.contactHero {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.contactHero h1 {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-lg);
}

.contactHero p {
  font-size: var(--fs-lg);
  line-height: 1.7;
}

/* Collaboration Approach Section */
.collaborationApproach {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.collaborationApproach h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-primary);
}

.collaborationApproach p {
  margin-bottom: var(--spacing-md);
  font-size: var(--fs-lg);
  line-height: 1.7;
  color: var(--color-text-secondary);
  text-align: left;
}

/* Contact Grid */
.contactGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
}

/* Contact Info */
.contactInfo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.contactCard {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--color-border);
  transition: transform var(--transition-normal);
}

.contactCard:last-of-type {
  border-bottom: none;
}

.contactCard:hover {
  transform: translateY(-3px);
}

.contactIcon {
  font-size: var(--fs-2xl);
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
}

.contactText {
  flex: 1;
}

.contactText h3 {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xs);
}

.contactText p {
  margin-bottom: var(--spacing-xs);
}

.contactText a {
  color: var(--color-primary);
  transition: color var(--transition-fast);
}

.contactText a:hover {
  color: var(--color-primary-light);
  text-decoration: underline;
}

.contactNote {
  font-size: var(--fs-sm);
  color: var(--color-text-light);
}

.contactInfo > .contactNote {
  padding: var(--spacing-lg) 0;
  margin-top: var(--spacing-md);
  border-top: 1px solid var(--color-border);
}

.contactInfo > .contactNote h3 {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
}

.contactInfo > .contactNote p {
  font-size: var(--fs-md);
  line-height: 1.7;
  color: var(--color-text);
}

/* Contact Form Container */
.contactFormContainer {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.contactFormContainer h2,
.contactInfo h2 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--spacing-sm);
}

.contactFormContainer > p,
.contactInfo > p {
  font-size: var(--fs-md);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-light);
}

/* Map Container */
.mapContainer {
  width: 100%;
  height: 450px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .contactGrid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }
}

@media (max-width: 768px) {
  .contactHero h1 {
    font-size: var(--fs-3xl);
  }

  .contactCard {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--spacing-md);
  }
}
