import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Section from '../components/ui/Section';
import {
  getConsentData,
  saveFullConsent,
  COOKIE_CATEGORIES
} from '../utils/cookieManager';
import { updateAnalyticsConsent } from '../utils/analytics';
import styles from './CookieSettings.module.css';

/**
 * Cookie Settings Page
 *
 * This page allows users to manage their cookie preferences.
 */
const CookieSettings = () => {
  const navigate = useNavigate();

  // State for cookie preferences
  const [preferences, setPreferences] = useState({
    [COOKIE_CATEGORIES.ESSENTIAL]: true, // Essential cookies are always enabled
    [COOKIE_CATEGORIES.PREFERENCES]: false,
    [COOKIE_CATEGORIES.ANALYTICS]: false,
    [COOKIE_CATEGORIES.MARKETING]: false
  });

  // State for showing success message
  const [showSuccess, setShowSuccess] = useState(false);

  // Load saved preferences on component mount
  useEffect(() => {
    const consentData = getConsentData();
    setPreferences(consentData.categories);
  }, []);

  // Handle preference toggle
  const handlePreferenceToggle = (category) => {
    if (category === COOKIE_CATEGORIES.ESSENTIAL) {
      return; // Essential cookies cannot be disabled
    }

    setPreferences(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  // Handle saving preferences
  const handleSavePreferences = () => {
    // Save preferences
    saveFullConsent(preferences);

    // Update analytics consent
    updateAnalyticsConsent(preferences[COOKIE_CATEGORIES.ANALYTICS]);

    // Show success message
    setShowSuccess(true);

    // Hide success message after 3 seconds
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  // Handle accepting all cookies
  const handleAcceptAll = () => {
    const allConsent = {
      [COOKIE_CATEGORIES.ESSENTIAL]: true,
      [COOKIE_CATEGORIES.PREFERENCES]: true,
      [COOKIE_CATEGORIES.ANALYTICS]: true,
      [COOKIE_CATEGORIES.MARKETING]: true
    };

    setPreferences(allConsent);
    saveFullConsent(allConsent);
    updateAnalyticsConsent(true);

    // Show success message
    setShowSuccess(true);

    // Hide success message after 3 seconds
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  // Handle accepting only essential cookies
  const handleAcceptEssential = () => {
    const essentialOnly = {
      [COOKIE_CATEGORIES.ESSENTIAL]: true,
      [COOKIE_CATEGORIES.PREFERENCES]: false,
      [COOKIE_CATEGORIES.ANALYTICS]: false,
      [COOKIE_CATEGORIES.MARKETING]: false
    };

    setPreferences(essentialOnly);
    saveFullConsent(essentialOnly);
    updateAnalyticsConsent(false);

    // Show success message
    setShowSuccess(true);

    // Hide success message after 3 seconds
    setTimeout(() => {
      setShowSuccess(false);
    }, 3000);
  };

  return (
    <div>
      <Section
        id="cookie-settings"
        title="Cookie Settings"
        subtitle="Manage your cookie preferences"
        padding="large"
        background="light"
        centered
      >
        <div className={styles.cookieSettingsContainer}>
          <p className={styles.cookieSettingsDescription}>
            This page allows you to manage your cookie preferences. You can choose which types of cookies you want to allow on this website.
            Essential cookies are necessary for the website to function properly and cannot be disabled.
          </p>

          <div className={styles.cookiePreferences}>
            <div className={styles.cookiePreferenceItem}>
              <div className={styles.cookiePreferenceHeader}>
                <h3 className={styles.cookiePreferenceTitle}>Essential Cookies</h3>
                <div className={`${styles.cookiePreferenceToggle} ${styles.disabled}`}>
                  <input
                    type="checkbox"
                    id="essential"
                    checked={preferences[COOKIE_CATEGORIES.ESSENTIAL]}
                    disabled
                  />
                  <label htmlFor="essential"></label>
                </div>
              </div>
              <p className={styles.cookiePreferenceDescription}>
                These cookies are necessary for the website to function properly and cannot be disabled. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in, or filling in forms.
              </p>
            </div>

            <div className={styles.cookiePreferenceItem}>
              <div className={styles.cookiePreferenceHeader}>
                <h3 className={styles.cookiePreferenceTitle}>Preference Cookies</h3>
                <div className={styles.cookiePreferenceToggle}>
                  <input
                    type="checkbox"
                    id="preferences"
                    checked={preferences[COOKIE_CATEGORIES.PREFERENCES]}
                    onChange={() => handlePreferenceToggle(COOKIE_CATEGORIES.PREFERENCES)}
                  />
                  <label htmlFor="preferences"></label>
                </div>
              </div>
              <p className={styles.cookiePreferenceDescription}>
                These cookies allow the website to remember choices you make (such as your preferred theme) and provide enhanced, more personal features. They may be set by us or by third-party providers whose services we have added to our pages.
              </p>
            </div>

            <div className={styles.cookiePreferenceItem}>
              <div className={styles.cookiePreferenceHeader}>
                <h3 className={styles.cookiePreferenceTitle}>Analytics Cookies</h3>
                <div className={styles.cookiePreferenceToggle}>
                  <input
                    type="checkbox"
                    id="analytics"
                    checked={preferences[COOKIE_CATEGORIES.ANALYTICS]}
                    onChange={() => handlePreferenceToggle(COOKIE_CATEGORIES.ANALYTICS)}
                  />
                  <label htmlFor="analytics"></label>
                </div>
              </div>
              <p className={styles.cookiePreferenceDescription}>
                These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously. They help us improve the website's functionality and your experience.
              </p>
            </div>

            <div className={styles.cookiePreferenceItem}>
              <div className={styles.cookiePreferenceHeader}>
                <h3 className={styles.cookiePreferenceTitle}>Marketing Cookies</h3>
                <div className={styles.cookiePreferenceToggle}>
                  <input
                    type="checkbox"
                    id="marketing"
                    checked={preferences[COOKIE_CATEGORIES.MARKETING]}
                    onChange={() => handlePreferenceToggle(COOKIE_CATEGORIES.MARKETING)}
                  />
                  <label htmlFor="marketing"></label>
                </div>
              </div>
              <p className={styles.cookiePreferenceDescription}>
                These cookies are used to track visitors across websites to display relevant advertisements. They may be set by us or by third-party providers whose services we have added to our pages.
              </p>
            </div>
          </div>

          <div className={styles.cookieSettingsButtons}>
            <button
              className={`${styles.cookieSettingsButton} ${styles.secondary}`}
              onClick={handleAcceptEssential}
            >
              Accept Essential Only
            </button>

            <button
              className={`${styles.cookieSettingsButton} ${styles.tertiary}`}
              onClick={handleAcceptAll}
            >
              Accept All
            </button>

            <button
              className={`${styles.cookieSettingsButton} ${styles.primary}`}
              onClick={handleSavePreferences}
            >
              Save Preferences
            </button>
          </div>

          {showSuccess && (
            <div className={styles.successMessage}>
              Your cookie preferences have been saved successfully.
            </div>
          )}

          <div className={styles.backLink}>
            <button
              className={styles.backButton}
              onClick={() => navigate(-1)}
            >
              &larr; Back to previous page
            </button>
          </div>
        </div>
      </Section>
    </div>
  );
};

export default CookieSettings;
