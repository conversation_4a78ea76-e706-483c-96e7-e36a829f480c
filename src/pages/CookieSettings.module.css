.cookieSettingsContainer {
  max-width: 800px;
  margin: 0 auto;
}

.cookieSettingsDescription {
  margin-bottom: 2rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.cookiePreferences {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.cookiePreferenceItem {
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: 1.5rem;
  background-color: var(--color-background-alt);
  transition: all 0.3s ease;
}

.cookiePreferenceItem:hover {
  box-shadow: var(--shadow-md);
}

.cookiePreferenceHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.cookiePreferenceTitle {
  font-size: 1.1rem;
  margin: 0;
  color: var(--color-text);
  font-weight: 600;
}

.cookiePreferenceDescription {
  font-size: 0.95rem;
  color: var(--color-text-secondary);
  margin: 0;
  line-height: 1.6;
}

.cookiePreferenceToggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.cookiePreferenceToggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.cookiePreferenceToggle label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-background);
  transition: .4s;
  border-radius: 34px;
  border: 1px solid var(--color-border);
}

.cookiePreferenceToggle label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 3px;
  background-color: var(--color-text-tertiary);
  transition: .4s;
  border-radius: 50%;
}

.cookiePreferenceToggle input:checked + label {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.cookiePreferenceToggle input:checked + label:before {
  transform: translateX(24px);
  background-color: white;
}

.cookiePreferenceToggle.disabled label {
  opacity: 0.7;
  cursor: not-allowed;
}

.cookieSettingsButtons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  justify-content: center;
}

.cookieSettingsButton {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 0.95rem;
  min-width: 180px;
}

.cookieSettingsButton.primary {
  background-color: var(--color-primary);
  color: white;
}

.cookieSettingsButton.primary:hover {
  background-color: var(--color-primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.cookieSettingsButton.secondary {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.cookieSettingsButton.secondary:hover {
  background-color: var(--color-background-alt);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.cookieSettingsButton.tertiary {
  background-color: var(--color-background-alt);
  color: var(--color-text);
}

.cookieSettingsButton.tertiary:hover {
  background-color: var(--color-background-alt-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.successMessage {
  background-color: var(--color-success-bg);
  color: var(--color-success);
  padding: 1rem;
  border-radius: var(--radius-md);
  text-align: center;
  margin-bottom: 1.5rem;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.backLink {
  text-align: center;
  margin-top: 2rem;
}

.backButton {
  background: none;
  border: none;
  color: var(--color-primary);
  font-size: 0.95rem;
  cursor: pointer;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
}

.backButton:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .cookieSettingsButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .cookieSettingsButton {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .cookiePreferenceHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .cookiePreferenceToggle {
    align-self: flex-start;
  }
  
  .cookiePreferenceItem {
    padding: 1rem;
  }
}
