import { FiBook, FiClock, FiUsers, FiStar, FiPlay, FiDownload, FiAward, FiTarget } from 'react-icons/fi';
import Section from '../components/ui/Section';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import NetworkBackground from '../components/ui/NetworkBackground';
import styles from './Courses.module.css';

const Courses = () => {
  const courses = [
    {
      id: 'ai-fundamentals',
      title: 'AI Fundamentals for Business',
      description: 'Master the basics of artificial intelligence and learn how to implement AI solutions in your business operations.',
      duration: '8 weeks',
      level: 'Beginner',
      students: 150,
      rating: 4.8,
      price: '₹12,999',
      originalPrice: '₹19,999',
      image: 'https://via.placeholder.com/400x200/0070f3/ffffff?text=AI+Fundamentals',
      features: [
        'Introduction to AI and Machine Learning',
        'ChatGPT and Gemini Google Integration',
        'Business Use Cases and Applications',
        'Hands-on Projects and Assignments',
        'Certificate of Completion',
        'Lifetime Access to Course Materials'
      ],
      prerequisites: [
        'Basic computer skills',
        'No programming experience required',
        'Enthusiasm to learn new technologies'
      ],
      instructor: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      category: 'AI & Technology'
    },
    {
      id: 'full-stack-development',
      title: 'Complete Full-Stack Development',
      description: 'Learn modern web development with React, Node.js, and build production-ready applications from scratch.',
      duration: '12 weeks',
      level: 'Intermediate',
      students: 89,
      rating: 4.9,
      price: '₹24,999',
      originalPrice: '₹34,999',
      image: 'https://via.placeholder.com/400x200/ff9500/ffffff?text=Full-Stack+Dev',
      features: [
        'React and Modern JavaScript',
        'Node.js and Express Backend',
        'Database Design and Management',
        'API Development and Integration',
        'Deployment and DevOps Basics',
        'Real-world Project Portfolio'
      ],
      prerequisites: [
        'Basic HTML, CSS, and JavaScript knowledge',
        'Understanding of programming concepts',
        'Familiarity with web browsers and development tools'
      ],
      instructor: 'Hrishikesh Mohite',
      category: 'Web Development'
    },
    {
      id: 'ai-in-education',
      title: 'AI in Education: Transform Learning',
      description: 'Discover how to leverage AI tools and technologies to enhance educational experiences and outcomes.',
      duration: '6 weeks',
      level: 'Beginner',
      students: 67,
      rating: 4.7,
      price: '₹9,999',
      originalPrice: '₹14,999',
      image: 'https://via.placeholder.com/400x200/ff6900/ffffff?text=AI+in+Education',
      features: [
        'AI Tools for Educators',
        'Personalized Learning Systems',
        'Automated Assessment and Feedback',
        'Educational Content Creation with AI',
        'Case Studies and Best Practices',
        'Implementation Strategies'
      ],
      prerequisites: [
        'Teaching or educational background preferred',
        'Basic technology literacy',
        'Interest in educational innovation'
      ],
      instructor: 'Hrishikesh Mohite',
      category: 'Education Technology'
    },
    {
      id: 'business-strategy-digital',
      title: 'Digital Business Strategy',
      description: 'Learn to develop and implement effective digital transformation strategies for modern businesses.',
      duration: '10 weeks',
      level: 'Advanced',
      students: 45,
      rating: 4.6,
      price: '₹18,999',
      originalPrice: '₹26,999',
      image: 'https://via.placeholder.com/400x200/6366f1/ffffff?text=Business+Strategy',
      features: [
        'Digital Transformation Frameworks',
        'Market Analysis and Competitive Intelligence',
        'Technology Integration Strategies',
        'Change Management and Leadership',
        'Performance Metrics and KPIs',
        'Strategic Planning and Execution'
      ],
      prerequisites: [
        'Business or management experience',
        'Understanding of basic business concepts',
        'Leadership or strategic planning background'
      ],
      instructor: 'Hrishikesh Mohite',
      category: 'Business Strategy'
    },
    {
      id: 'ui-ux-design-mastery',
      title: 'UI/UX Design Mastery',
      description: 'Master user interface and user experience design principles to create engaging digital products.',
      duration: '8 weeks',
      level: 'Intermediate',
      students: 78,
      rating: 4.8,
      price: '₹16,999',
      originalPrice: '₹23,999',
      image: 'https://via.placeholder.com/400x200/ec4899/ffffff?text=UI/UX+Design',
      features: [
        'Design Thinking and User Research',
        'Wireframing and Prototyping',
        'Visual Design and Typography',
        'Usability Testing and Optimization',
        'Design Systems and Style Guides',
        'Portfolio Development'
      ],
      prerequisites: [
        'Basic design software knowledge',
        'Creative mindset and attention to detail',
        'Understanding of web and mobile interfaces'
      ],
      instructor: 'Hrishikesh Mohite',
      category: 'Design'
    },
    {
      id: 'tech-entrepreneurship',
      title: 'Tech Entrepreneurship Bootcamp',
      description: 'Learn how to build, launch, and scale technology-driven businesses in the modern digital economy.',
      duration: '6 weeks',
      level: 'Intermediate',
      students: 34,
      rating: 4.9,
      price: '₹21,999',
      originalPrice: '₹29,999',
      image: 'https://via.placeholder.com/400x200/f59e0b/ffffff?text=Tech+Entrepreneurship',
      features: [
        'Startup Ideation and Validation',
        'Technology Stack Selection',
        'MVP Development and Testing',
        'Funding and Investment Strategies',
        'Marketing and Growth Hacking',
        'Scaling and Team Building'
      ],
      prerequisites: [
        'Basic business understanding',
        'Entrepreneurial mindset',
        'Some technical or business background'
      ],
      instructor: 'Hrishikesh Mohite',
      category: 'Entrepreneurship'
    }
  ];

  const categories = [
    'All Courses',
    'AI & Technology',
    'Web Development',
    'Education Technology',
    'Business Strategy',
    'Design',
    'Entrepreneurship'
  ];

  const stats = [
    { icon: <FiUsers />, value: '500+', label: 'Students Enrolled' },
    { icon: <FiBook />, value: '15+', label: 'Courses Available' },
    { icon: <FiAward />, value: '95%', label: 'Completion Rate' },
    { icon: <FiStar />, value: '4.8', label: 'Average Rating' }
  ];

  return (
    <div className={styles.courses}>
      {/* Hero Section */}
      <Section
        id="courses-hero"
        padding="large"
        background="light"
        className={styles.heroSection}
      >
        <div className={styles.networkBackgroundContainer}>
          <NetworkBackground
            color="#0070f3"
            shape="courses"
            pointCount={60}
            connectionDistance={150}
            baseOpacity={0.3}
            highlightOpacity={0.8}
          />
        </div>
        <div className={styles.coursesHero}>
          <h1>Transform Your Skills with Expert-Led Courses</h1>
          <p>
            Master cutting-edge technologies and business strategies with comprehensive courses designed by industry experts. 
            From AI and full-stack development to business strategy and design, accelerate your career with practical, 
            hands-on learning experiences.
          </p>
          <div className={styles.heroStats}>
            {stats.map((stat, index) => (
              <div key={index} className={styles.statItem}>
                <div className={styles.statIcon}>{stat.icon}</div>
                <div className={styles.statValue}>{stat.value}</div>
                <div className={styles.statLabel}>{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </Section>

      {/* Course Categories */}
      <Section id="course-categories" background="none">
        <div className={styles.categoriesSection}>
          <h2>Course Categories</h2>
          <div className={styles.categoriesGrid}>
            {categories.slice(1).map((category, index) => (
              <div key={index} className={styles.categoryCard}>
                <h3>{category}</h3>
                <p>Explore courses in {category.toLowerCase()}</p>
              </div>
            ))}
          </div>
        </div>
      </Section>

      {/* Featured Courses */}
      <Section id="featured-courses" background="light">
        <div className={styles.coursesSection}>
          <div className={styles.sectionHeader}>
            <h2>Featured Courses</h2>
            <p>Discover our most popular and highly-rated courses designed to accelerate your professional growth</p>
          </div>
          
          <div className={styles.coursesGrid}>
            {courses.map((course) => (
              <Card
                key={course.id}
                className={styles.courseCard}
                variant="course"
              >
                <div className={styles.courseImage}>
                  <img src={course.image} alt={course.title} />
                  <div className={styles.courseLevel}>{course.level}</div>
                </div>
                
                <div className={styles.courseContent}>
                  <div className={styles.courseCategory}>{course.category}</div>
                  <h3 className={styles.courseTitle}>{course.title}</h3>
                  <p className={styles.courseDescription}>{course.description}</p>
                  
                  <div className={styles.courseStats}>
                    <div className={styles.courseStat}>
                      <FiClock size={16} />
                      <span>{course.duration}</span>
                    </div>
                    <div className={styles.courseStat}>
                      <FiUsers size={16} />
                      <span>{course.students} students</span>
                    </div>
                    <div className={styles.courseStat}>
                      <FiStar size={16} />
                      <span>{course.rating}</span>
                    </div>
                  </div>
                  
                  <div className={styles.coursePrice}>
                    <span className={styles.currentPrice}>{course.price}</span>
                    <span className={styles.originalPrice}>{course.originalPrice}</span>
                  </div>
                  
                  <div className={styles.courseActions}>
                    <Button variant="primary" className={styles.enrollButton}>
                      <FiPlay size={16} />
                      Enroll Now
                    </Button>
                    <Button variant="outline" className={styles.detailsButton}>
                      View Details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </Section>

      {/* Call to Action */}
      <Section id="courses-cta" background="none" className={styles.ctaSection}>
        <div className={styles.cta}>
          <h2>Ready to Start Your Learning Journey?</h2>
          <p>
            Join hundreds of professionals who have transformed their careers with our expert-led courses. 
            Get personalized mentorship, hands-on projects, and industry-recognized certifications.
          </p>
          <div className={styles.ctaButtons}>
            <Button variant="primary" size="large">
              Browse All Courses
            </Button>
            <Button variant="outline" size="large">
              Contact for Custom Training
            </Button>
          </div>
        </div>
      </Section>
    </div>
  );
};

export default Courses;
