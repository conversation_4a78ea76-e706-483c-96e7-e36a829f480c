.courses {
  width: 100%;
}

/* Hero Section */
.heroSection {
  position: relative;
  overflow: hidden;
  min-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.networkBackgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.coursesHero {
  text-align: center;
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.coursesHero h1 {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-lg);
  color: var(--color-text);
}

.coursesHero p {
  font-size: var(--fs-lg);
  line-height: 1.7;
  margin-bottom: var(--spacing-2xl);
  color: var(--color-text-light);
}

/* Hero Stats */
.heroStats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
}

.statItem {
  text-align: center;
  padding: var(--spacing-lg);
  background-color: rgba(var(--color-primary-rgb), 0.05);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(var(--color-primary-rgb), 0.1);
  transition: all var(--transition-normal);
}

.statItem:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  background-color: rgba(var(--color-primary-rgb), 0.1);
}

.statIcon {
  font-size: var(--fs-2xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
  display: flex;
  justify-content: center;
}

.statValue {
  font-size: var(--fs-2xl);
  font-weight: var(--fw-bold);
  color: var(--color-text);
  margin-bottom: var(--spacing-xs);
}

.statLabel {
  font-size: var(--fs-sm);
  color: var(--color-text-light);
  font-weight: var(--fw-medium);
}

/* Categories Section */
.categoriesSection {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.categoriesSection h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text);
}

.categoriesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.categoryCard {
  background-color: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  transition: all var(--transition-normal);
  cursor: pointer;
}

.categoryCard:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: var(--color-primary);
}

.categoryCard h3 {
  font-size: var(--fs-xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
}

.categoryCard p {
  color: var(--color-text-light);
  font-size: var(--fs-md);
}

/* Courses Section */
.coursesSection {
  width: 100%;
}

.sectionHeader {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-2xl);
}

.sectionHeader h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.sectionHeader p {
  font-size: var(--fs-lg);
  color: var(--color-text-light);
  line-height: 1.6;
}

.coursesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-xl);
}

/* Course Card */
.courseCard {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border);
}

.courseCard:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.courseImage {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.courseImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.courseCard:hover .courseImage img {
  transform: scale(1.05);
}

.courseLevel {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background-color: var(--color-primary);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--fs-xs);
  font-weight: var(--fw-semibold);
}

.courseContent {
  padding: var(--spacing-xl);
}

.courseCategory {
  font-size: var(--fs-sm);
  color: var(--color-primary);
  font-weight: var(--fw-semibold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-sm);
}

.courseTitle {
  font-size: var(--fs-xl);
  color: var(--color-text);
  margin-bottom: var(--spacing-md);
  line-height: 1.3;
}

.courseDescription {
  color: var(--color-text-light);
  font-size: var(--fs-md);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.courseStats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--color-border);
}

.courseStat {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--fs-sm);
  color: var(--color-text-light);
}

.courseStat svg {
  color: var(--color-primary);
}

.coursePrice {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.currentPrice {
  font-size: var(--fs-xl);
  font-weight: var(--fw-bold);
  color: var(--color-primary);
}

.originalPrice {
  font-size: var(--fs-md);
  color: var(--color-text-lighter);
  text-decoration: line-through;
}

.courseActions {
  display: flex;
  gap: var(--spacing-sm);
}

.enrollButton,
.detailsButton {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--fs-sm);
  font-weight: var(--fw-semibold);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.enrollButton {
  background-color: var(--color-primary);
  color: white;
  border: none;
}

.enrollButton:hover {
  background-color: var(--color-primary-light);
  transform: translateY(-2px);
}

.detailsButton {
  background-color: transparent;
  color: var(--color-primary);
  border: 1px solid var(--color-primary);
}

.detailsButton:hover {
  background-color: var(--color-primary);
  color: white;
}

/* CTA Section */
.ctaSection {
  background-color: var(--color-background-alt);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-3xl) 0;
}

.cta h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.cta p {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-2xl);
  color: var(--color-text-light);
  line-height: 1.7;
}

.ctaButtons {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: center;
}

.ctaButtons button {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--fs-md);
  font-weight: var(--fw-semibold);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .heroStats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-lg);
  }
  
  .coursesGrid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }
  
  .categoriesGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .ctaButtons {
    flex-direction: column;
    align-items: center;
  }
  
  .ctaButtons button {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .coursesHero h1 {
    font-size: var(--fs-3xl);
  }
  
  .heroStats {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .statItem {
    padding: var(--spacing-md);
  }
  
  .coursesGrid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .courseActions {
    flex-direction: column;
  }
  
  .sectionHeader h2 {
    font-size: var(--fs-2xl);
  }
  
  .cta h2 {
    font-size: var(--fs-2xl);
  }
}

@media (max-width: 480px) {
  .courseContent {
    padding: var(--spacing-lg);
  }
  
  .courseStats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .coursePrice {
    justify-content: center;
  }
}
