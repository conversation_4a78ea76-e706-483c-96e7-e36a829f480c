import { FiCode, FiCpu, FiBriefcase, FiPenTool, FiVideo, FiLayout, FiBook } from 'react-icons/fi';
import { lazy, Suspense } from 'react';
import Section from '../components/ui/Section';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import AnimatedText from '../components/ui/AnimatedText';
import OptimizedHeroBackground from '../components/ui/OptimizedHeroBackground';
import { useTheme } from '../hooks/useTheme';
import styles from './Home.module.css';

// Lazy load non-critical components
const TechRibbon = lazy(() => import('../components/ui/TechRibbon'));
const ToolsRibbon = lazy(() => import('../components/ui/ToolsRibbon'));

// Import local images
import profileImage from '../assets/images/hero/optimized/Hrishikesh-Mohite-professional-portrait.png';
import ai2 from '../assets/images/projects/optimized/Hrishikesh-Mohite-Neural-Network-Playground.png';
import nlpApi from '../assets/images/projects/optimized/Hrishikesh-Mohite-nlp-api.png';
import careerCraft from '../assets/images/projects/optimized/Hrishikesh-Mohite-CareerCraft.png';

const Home = () => {
  const { theme } = useTheme();
  const roles = [
    'Entrepreneur',
    'AI Strategist',
    'Developer',
    'Designer',
    'Tech Mentor',
    'Author'
  ];

  const services = [
    {
      title: 'AI Development & Strategy',
      icon: <FiCpu />,
      description: 'Custom AI solutions and strategic consulting to transform your business with cutting-edge technology.'
    },
    {
      title: 'Full-Stack Development',
      icon: <FiCode />,
      description: 'End-to-end web and mobile application development with modern technologies and best practices.'
    },
    {
      title: 'Tech Trainer & Mentor',
      icon: <FiBook />,
      description: 'Empowering aspiring developers, designers, and entrepreneurs with the knowledge and skills needed to excel in the digital landscape.'
    },
    {
      title: 'Business Strategy',
      icon: <FiBriefcase />,
      description: 'Strategic business consulting to help you navigate challenges and capitalize on opportunities.'
    },
    {
      title: 'Graphic Design',
      icon: <FiPenTool />,
      description: 'Creative visual solutions including branding, marketing materials, and digital assets.'
    },
    {
      title: 'Video Production',
      icon: <FiVideo />,
      description: 'Professional video editing, motion graphics, and creative direction for compelling visual storytelling.'
    },
    {
      title: 'Web & UI/UX Design',
      icon: <FiLayout />,
      description: 'User-centered design that combines aesthetics with functionality for exceptional digital experiences.'
    }
  ];

  const featuredProjects = [
    {
      title: 'Natural Language Processing API',
      category: 'AI Development',
      image: nlpApi,
      altText: 'Natural Language Processing API by Hrishikesh Mohite - AI-powered text analysis and sentiment analysis API service'
    },
    {
      title: 'Neural Network Playground',
      category: 'AI Development',
      image: ai2,
      altText: 'Neural Network Playground by Hrishikesh Mohite - Interactive AI education tool for machine learning visualization'
    },
    {
      title: 'CareerCraft',
      category: 'Web Development',
      image: careerCraft,
      altText: 'CareerCraft web application by Hrishikesh Mohite - AI-powered career development platform for job seekers'
    }
  ];

  return (
    <div className={styles.home}>
      {/* Hero Section */}
      <section className={styles.hero}>
        <div className={styles.heroBackgroundContainer}>
          <OptimizedHeroBackground
            primaryColor={theme === 'dark' ? "#0070f3" : "#0050b3"}
            secondaryColor={theme === 'dark' ? "#3291ff" : "#ff9500"}
            pointCount={60}
            connectionDistance={120}
            baseOpacity={theme === 'dark' ? 0.3 : 0.4}
            highlightOpacity={theme === 'dark' ? 0.8 : 0.9}
            particleSize={{ min: 1, max: 3 }}
            particleSpeed={{ min: 0.1, max: 0.3 }}
            dataNodeCount={3}
            dataNodeSize={5}
          />
        </div>
        <div className={styles.heroOverlay}></div>
        <div className={styles.heroContent}>
          <div className={`container ${styles.heroContainer}`}>
            <div className={styles.heroText}>
              <h1 className={styles.heroTitle}>
                Hrishikesh Mohite | AI Strategist, Education & Tech Innovator
              </h1>
              <h2 className={styles.heroSubtitle}>
                Transforming Businesses with Tailored Tech Solutions and Expert Mentorship
              </h2>
              <h3 className={styles.heroRoles}>
                <AnimatedText
                  words={roles}
                  speed={2000}
                  prefix="I'm a "
                />
              </h3>
              <div className={styles.heroButtons}>
                <Button to="/contact" size="large">
                  Work With Me
                </Button>
                <Button to="/portfolio" variant="outline" size="large">
                  View My Work
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <Section
        id="about-preview"
        padding="large"
        className={styles.aboutSection}
      >
        <div className={styles.aboutHero}>
          <div className={styles.aboutImage}>
            <img
              src={profileImage}
              alt="Hrishikesh Mohite - Professional Portrait of AI Developer, Full-Stack Expert & Tech Mentor"
              className={styles.profilePhoto}
              style={{
                objectFit: 'cover',
                imageRendering: 'auto',
                colorRendering: 'optimizeQuality',
                colorInterpolation: 'linearRGB',
                colorAdjust: 'exact',
                filter: 'none',
                WebkitFilter: 'none'
              }}
            />
            <div className={styles.aboutImageOverlay}>
              <div className={styles.aboutImageInfo}>
                <h3>Hrishikesh Mohite</h3>
                <p>Entrepreneur & Tech Mentor</p>
              </div>
            </div>
          </div>
          <div className={styles.aboutContent}>
            <h2 className={styles.aboutTitle}>AI Strategist & Full-Stack Development Expertise</h2>
            <h3 className={styles.aboutSubtitle}>Entrepreneur, AI Strategist, Full-Stack Developer, Designer & Tech Mentor</h3>
            <p>
            Hello, I'm Hrishikesh Mohite. As an AI strategist and tech innovator, I transform ideas into impactful digital solutions. My expertise spans over a decade, focusing on cutting-edge Artificial Intelligence applications that drive measurable ROI.

I specialize in integrating AI in education, developing custom machine learning models, and building scalable full-stack applications with React, Node.js, and AI. Deeply engaged with advancements like Gemini Google, I ensure future-proof tech solutions. I also empower through tech mentorship, helping master what is AI and other core technologies. Whether for business transformation or custom app development, my interdisciplinary approach combines technical excellence with strategic thinking. Discover how AI can elevate your business.
            </p>
            <Button to="/about" variant="primary">
              Learn About My Journey
            </Button>
          </div>
        </div>
      </Section>

      {/* Services Section */}
      <Section
        id="services-preview"
        title="Custom AI Solutions & Development Services"
        subtitle="Expert AI in education, tech mentorship & full-stack development"
        background="light"
        centered
      >
        <div className={styles.servicesGrid}>
          {services.map((service, index) => (
            <Card
              key={index}
              title={service.title}
              icon={service.icon}
              variant="service"
              to={`/services#${service.title.toLowerCase().replace(/\s+/g, '-')}`}
            >
              <p>{service.description}</p>
            </Card>
          ))}
        </div>
        <div className={styles.sectionCta}>
          <Button to="/services" variant="outline">
            Explore All AI Services
          </Button>
        </div>
      </Section>

      {/* Portfolio Preview Section */}
      <Section
        id="portfolio-preview"
        title="Featured AI & Technology Projects"
        subtitle="Custom machine learning solutions & full-stack applications"
        centered
      >
        <div className={styles.portfolioGrid}>
          {featuredProjects.map((project, index) => (
            <Card
              key={index}
              title={project.title}
              subtitle={project.category}
              image={project.image}
              altText={project.altText}
              variant="project"
              to={`/portfolio`}
            />
          ))}
        </div>
        <div className={styles.sectionCta}>
          <Button to="/portfolio">
            View Complete Portfolio
          </Button>
        </div>
      </Section>

      {/* Tech Ribbon Section */}
      <Suspense fallback={<div className={styles.ribbonPlaceholder}></div>}>
        <TechRibbon />
      </Suspense>

      {/* Tools Ribbon Section */}
      <Suspense fallback={<div className={styles.ribbonPlaceholder}></div>}>
        <ToolsRibbon />
      </Suspense>

      {/* CTA Section */}
      <Section
        id="cta"
        padding="large"
        background="none"
        className={styles.ctaSection}
      >
        <div className={styles.cta}>
          <h2>Professional Tech Mentorship & AI Consultation</h2>
          <p>Transform your business with custom AI solutions and expert tech mentorship. Let's collaborate on your next innovative project.</p>
          <Button to="/contact" variant="primary" size="large">
            Start Your AI Project
          </Button>
        </div>
      </Section>
    </div>
  );
};

export default Home;
