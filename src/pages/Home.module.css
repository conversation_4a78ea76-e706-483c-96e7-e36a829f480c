.home {
  width: 100%;
}

/* Hero Section */
.hero {
  position: relative;
  height: 100vh;
  min-height: 600px;
  display: flex;
  align-items: center;
  background-color: var(--color-primary-dark);
  color: white;
  overflow: hidden;
}

.heroBackgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.heroOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.2));
  z-index: 1;
}

.heroContent {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: var(--spacing-2xl) 0;
}

.heroContainer {
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroText {
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  max-width: 800px;
  margin: 0 auto;
}

.heroTitle {
  font-size: var(--fs-5xl);
  font-weight: var(--fw-bold);
  margin-bottom: var(--spacing-md);
  color: transparent;
}

.heroSubtitle {
  font-size: var(--fs-2xl);
  font-weight: var(--fw-medium);
  margin-bottom: var(--spacing-md);
  color: transparent;
}

.heroRoles {
  font-size: var(--fs-xl);
  font-weight: var(--fw-medium);
  margin-bottom: var(--spacing-xl);
  color: var(--color-secondary);
}

.heroButtons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

/* Hero section animation */
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.heroTitle, .heroSubtitle {
  background: linear-gradient(90deg,
    rgba(255,255,255,1) 0%,
    rgba(255,255,255,0.9) 50%,
    rgba(255,255,255,1) 100%);
  background-size: 200% auto;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradientAnimation 8s ease infinite;
}

/* About Section */
.aboutSection {
  padding: var(--spacing-2xl) 0;
  width: 100%;
  position: relative;
}

/* About Hero Section */
.aboutHero {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-2xl);
  align-items: center;
}

.aboutContent {
  padding-left: var(--spacing-lg);
}

.aboutContent h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
}

.aboutContent .aboutSubtitle {
  font-size: var(--fs-xl);
  font-weight: var(--fw-medium);
  color: var(--color-primary);
  margin-top: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
}

.aboutContent p {
  font-size: var(--fs-lg);
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
}

.ajinkyaLink {
  position: relative;
  color: var(--color-primary);
  font-weight: var(--fw-semibold);
  transition: color var(--transition-fast);
  padding: 0 2px;
}

.ajinkyaLink::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.ajinkyaLink:hover {
  color: var(--color-primary-dark);
}

.ajinkyaLink:hover::after {
  width: 100%;
}

.aboutTitle {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  position: relative;
  display: inline-block;
}

.aboutTitle::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--color-primary);
}

.aboutImage {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  max-width: 100%;
  margin: 0 auto;
}

.aboutImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Further refined gradient to preserve natural colors, especially around lips */
  background: linear-gradient(to top, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.1) 60%, transparent 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
  /* Ensure we're not affecting color saturation */
  mix-blend-mode: normal;
  pointer-events: none;
}

.aboutImage img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform var(--transition-normal);
  /* Ensure color fidelity is preserved */
  filter: none;
  -webkit-filter: none;
  /* Prevent any color shifts */
  color-rendering: optimizeQuality;
  image-rendering: optimizeQuality;
}

/* Enhanced styles for profile photo to maintain color fidelity */
.profilePhoto {
  filter: none !important;
  -webkit-filter: none !important;
  image-rendering: auto !important;
  color-rendering: optimizeQuality !important;
  color-interpolation: linearRGB !important;
  color-adjust: exact !important;
  color-space: srgb !important;
  /* Prevent any browser-specific color adjustments */
  -webkit-font-smoothing: auto !important;
  -moz-osx-font-smoothing: auto !important;
}

.ribbonPlaceholder {
  height: 100px;
  width: 100%;
  background-color: var(--color-background-alt);
  margin: var(--spacing-xl) 0;
}

.aboutImageOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-md);
  z-index: 2;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--transition-normal), transform var(--transition-normal);
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  /* Ensure overlay doesn't affect image colors */
  pointer-events: none;
  mix-blend-mode: normal;
}

.aboutImageInfo {
  text-align: left;
  color: white;
  padding-left: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
}

.aboutImageInfo h3 {
  font-size: var(--fs-xl);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--fw-bold);
}

.aboutImageInfo p {
  font-size: var(--fs-md);
  opacity: 0.9;
}

.aboutImage:hover {
  box-shadow: var(--shadow-xl);
}

.aboutImage:hover::before {
  opacity: 1;
}

.aboutImage:hover .aboutImageOverlay {
  opacity: 1;
  transform: translateY(0);
}

.aboutImage:hover img {
  transform: scale(1.03);
  /* Ensure no color distortion on hover */
  filter: none;
  -webkit-filter: none;
}

/* Services Grid */
.servicesGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* Portfolio Grid */
.portfolioGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  /* Ensure proper spacing for square cards */
  grid-auto-rows: 1fr;
}

/* Section CTA */
.sectionCta {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

/* CTA Section */
.ctaSection {
  background-color: var(--color-background);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.ctaSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.cta h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.cta p {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-light);
}

.cta button {
  position: relative;
  overflow: hidden;
  font-weight: var(--fw-bold);
  letter-spacing: 0.5px;
  padding: calc(var(--spacing-md) + 2px) calc(var(--spacing-xl) + 5px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.cta button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .heroTitle {
    font-size: var(--fs-4xl);
  }

  .heroSubtitle {
    font-size: var(--fs-xl);
  }

  .heroRoles {
    font-size: var(--fs-lg);
  }

  .aboutHero {
    gap: var(--spacing-xl);
  }

  .aboutContent {
    padding-left: var(--spacing-md);
  }

  .ajinkyaLink {
    padding: 0 1px;
  }

  .aboutImage {
    max-width: 400px;
    margin: 0 auto;
  }

  .aboutImageInfo h3 {
    font-size: var(--fs-lg);
  }

  .servicesGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .portfolioGrid {
    grid-template-columns: repeat(2, 1fr);
    /* Maintain square aspect ratio on tablets */
    grid-auto-rows: 1fr;
  }
}

@media (max-width: 768px) {
  .heroTitle {
    font-size: var(--fs-3xl);
  }

  .heroSubtitle {
    font-size: var(--fs-lg);
  }

  .heroRoles {
    font-size: var(--fs-md);
  }

  .heroText {
    padding: 0 var(--spacing-md);
  }

  .aboutHero {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .aboutContent {
    order: 2;
    padding: 0 var(--spacing-md);
  }

  .ajinkyaLink {
    display: inline-block;
    padding: 0 2px;
    margin: 0 1px;
  }

  .aboutTitle {
    margin-top: var(--spacing-lg);
  }

  .aboutImage {
    order: 1;
    max-width: 260px;
    margin: 0 auto;
  }

  .aboutImageInfo {
    padding-left: var(--spacing-sm);
  }

  .aboutImageInfo h3 {
    font-size: var(--fs-lg);
  }

  .aboutImageInfo p {
    font-size: var(--fs-sm);
  }

  .servicesGrid {
    grid-template-columns: 1fr;
  }

  .portfolioGrid {
    grid-template-columns: 1fr;
    /* Maintain square aspect ratio on mobile */
    grid-auto-rows: 1fr;
  }

  .heroButtons {
    flex-direction: column;
  }
}
