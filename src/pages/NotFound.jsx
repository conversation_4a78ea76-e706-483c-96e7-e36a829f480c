import { Link } from 'react-router-dom';
import Button from '../components/ui/Button';
import styles from './NotFound.module.css';

const NotFound = () => {
  return (
    <div className={styles.notFound}>
      <div className={styles.content}>
        <h1>404</h1>
        <h2>Page Not Found</h2>
        <p>
          The page you are looking for might have been removed, had its name changed,
          or is temporarily unavailable.
        </p>
        <div className={styles.actions}>
          <Button to="/" variant="primary" size="large">
            Back to Home
          </Button>
          <Button to="/contact" variant="outline" size="large">
            Contact Me
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
