.notFound {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 80px);
  padding: var(--spacing-2xl) var(--spacing-md);
  text-align: center;
}

.content {
  max-width: 600px;
}

.notFound h1 {
  font-size: 10rem;
  font-weight: var(--fw-bold);
  color: var(--color-primary);
  line-height: 1;
  margin-bottom: var(--spacing-md);
}

.notFound h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-lg);
}

.notFound p {
  font-size: var(--fs-lg);
  color: var(--color-text-light);
  margin-bottom: var(--spacing-xl);
}

.actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

@media (max-width: 768px) {
  .notFound h1 {
    font-size: 6rem;
  }

  .notFound h2 {
    font-size: var(--fs-2xl);
  }

  .actions {
    flex-direction: column;
  }
}
