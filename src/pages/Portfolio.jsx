import { useState } from 'react';
import { FiExternalLink } from 'react-icons/fi';
import Section from '../components/ui/Section';
import Button from '../components/ui/Button';
import NetworkBackground from '../components/ui/NetworkBackground';
import styles from './Portfolio.module.css';

// Import local project images
import web1 from '../assets/images/projects/Hrishikesh-Mohite-CareerCraft.png';
import web2 from '../assets/images/projects/optimized/Hrishikesh-Mohite-Neural-Network-Playground.png';
import web3 from '../assets/images/projects/Hrishikesh-Mohite-dcvc-venture-capital.png';
import ai1 from '../assets/images/projects/optimized/Hrishikesh-Mohite-predictive-analytics-dashboard.png';
import ai2 from '../assets/images/projects/optimized/Hrishikesh-Mohite-nlp-api.png';
import design2 from '../assets/images/projects/Hrishikesh-Mohite-mobile-app-ui-design.png';
import design4 from '../assets/images/projects/Hrishikesh-Mohite-3d-product-visualization.jpg';
import video1 from '../assets/images/projects/Hrishikesh-Mohite-product-explainer-video.jpg';
import video2 from '../assets/images/projects/Hrishikesh-Mohite-corporate-brand-video.webp';

// Project images object
const projectImages = {
  web1, web2, web3, ai1, ai2, design2, design4, video1, video2
};

const Portfolio = () => {
  const [filter, setFilter] = useState('all');
  const [selectedProject, setSelectedProject] = useState(null);

  const projects = [
    {
      id: 1,
      title: 'CareerCraft',
      category: 'web',
      image: projectImages.web1,
      altText: 'CareerCraft web application by Hrishikesh Mohite - AI-powered career development platform with React and Node.js for job tracking and interview preparation',
      description: 'A comprehensive career development platform built with React and Node.js, designed to help job seekers track applications, prepare for interviews, and manage their professional growth. Features include an AI-powered resume builder, interactive job application tracker, personalized interview preparation tools, and customized career roadmap planning with progress analytics.',
      technologies: ['React', 'Node.js', 'Express', 'MongoDB', 'Material UI', 'JWT Authentication', 'Redux', 'Axios', 'Chart.js', 'React Router', 'Mongoose'],
      link: 'https://github.com/Lucifer88484/CareerCraft.git'
    },
    {
      id: 2,
      title: 'Neural Network Playground',
      category: 'ai',
      image: projectImages.web2,
      altText: 'Neural Network Playground by Hrishikesh Mohite - Interactive AI education tool for visualizing machine learning with TensorFlow.js and React',
      description: 'An interactive web application that allows users to visualize and experiment with neural networks in real-time. This educational tool helps users understand the fundamentals of deep learning by providing a hands-on environment to build, train, and test neural network architectures with customizable parameters, datasets, and visualization tools.',
      technologies: ['JavaScript', 'TensorFlow.js', 'React', 'D3.js', 'HTML5 Canvas', 'CSS3', 'WebGL', 'Node.js', 'Express'],
      link: 'https://github.com/Lucifer88484/Neural-Network-Playground'
    },
    {
      id: 3,
      title: 'DCVC (Data Collective Venture Capital)',
      category: 'web',
      image: projectImages.web3,
      altText: 'DCVC venture capital website by Hrishikesh Mohite - Deep tech investment platform built with React and TypeScript for data-driven startups',
      description: 'DCVC (Data Collective Venture Capital) is a deep tech-focused venture capital firm investing in startups that leverage advanced science, engineering, and data to solve complex global challenges.',
      technologies: ['React', 'TypeScript', 'Tailwind CSS', 'Vite', 'ECharts', 'Node.js', 'Express', 'Python', 'TensorFlow', 'MongoDB'],
      link: 'https://www.dcvc.com/'
    },
    {
      id: 4,
      title: 'PredictX – AI Predictive Analytics Dashboard',
      category: 'ai',
      image: projectImages.ai1,
      altText: 'PredictX AI analytics dashboard by Hrishikesh Mohite - Machine learning predictive analytics platform with Python and TensorFlow for business intelligence',
      description: 'PredictX is an AI-powered analytics dashboard by Hrishikesh D. Mohite, built to help businesses visualize data, forecast trends, and make smarter decisions — all through a modern, responsive interface with interactive charts and AI-driven insights.',
      technologies: ['Python', 'TensorFlow', 'React', 'D3.js'],
      link: 'https://github.com/Lucifer88484/PredictX.git'
    },
    {
      id: 5,
      title: 'Natural Language Processing API',
      category: 'ai',
      image: projectImages.ai2,
      altText: 'NLP API service by Hrishikesh Mohite - Natural language processing API with Python and TensorFlow for sentiment analysis and text classification',
      description: 'An API service that processes and analyzes human language data using NLP techniques such as sentiment analysis, entity recognition, text classification, and language detection, enabling integration into various applications for enhanced language understanding.',
      technologies: ['Python', 'SpaCy', 'NLTK', 'TensorFlow', 'Flask', 'FastAPI', 'Docker', 'REST API'],
      link: 'https://github.com/Lucifer88484/Natural-Language-Processing-API.git'
    },

    {
      id: 6,
      title: 'Mobile App UI Design',
      category: 'design',
      image: projectImages.design2,
      altText: 'Mobile app UI design by Hrishikesh Mohite - Modern user interface design with Figma for intuitive mobile application user experience',
      description: 'A modern mobile application interface designed by Hrishikesh Mohite, focusing on clean layouts, intuitive navigation, and user-centric interactions. The project emphasizes visual hierarchy, responsive components, and a cohesive design system suitable for various mobile platforms.',
      technologies: ['Figma', 'Adobe Illustrator', 'Jetpack Compose'],
      link: 'https://www.behance.net/gallery/226151021/Mobile-App-UI-Design'
    },
    {
      id: 7,
      title: '3D Product Visualization',
      category: 'design',
      image: projectImages.design4,
      altText: '3D product visualization by Hrishikesh Mohite - High-quality 3D rendering and modeling with Blender and Cinema 4D for marketing and e-commerce',
      description: 'High-quality 3D models and renders created for use in marketing campaigns, product launches, and digital advertisements. These visuals enhance customer engagement by showcasing products in realistic and dynamic environments, suitable for e-commerce, AR/VR, and promotional content.',
      technologies: ['Blender', 'Cinema 4D', 'Adobe Dimension', 'Photoshop'],
      link: 'https://www.behance.net/gallery/226154029/3D-Product-Visualization'
    },

    {
      id: 8,
      title: 'Product Explainer Video',
      category: 'video',
      image: projectImages.video1,
      altText: 'Product explainer video by Hrishikesh Mohite - Animated marketing video with After Effects and motion graphics for product demonstration',
      description: 'Animated explainer video showcasing a product\'s features, benefits, and use cases in an engaging format.',
      technologies: ['After Effects', 'Premiere Pro', 'Motion Graphics'],
      link: 'https://www.behance.net/gallery/*********/Product-Explainer-Video'
    },
    {
      id: 9,
      title: 'Corporate Brand Video',
      category: 'video',
      image: projectImages.video2,
      altText: 'Corporate brand video by Hrishikesh Mohite - Professional video production and storytelling for business branding and marketing',
      description: 'Professional brand video telling the story of a company\'s mission, values, and impact through compelling visuals.',
      technologies: ['Video Production', 'Storytelling', 'Editing'],
      link: 'https://www.behance.net/gallery/*********/Corporate-Brand-Video'
    }
  ];

  const filteredProjects = filter === 'all'
    ? projects
    : projects.filter(project => project.category === filter);

  const openProjectModal = (project) => {
    setSelectedProject(project);
    document.body.style.overflow = 'hidden';
  };

  const closeProjectModal = () => {
    setSelectedProject(null);
    document.body.style.overflow = 'auto';
  };

  return (
    <div className={styles.portfolio}>
      {/* Hero Section */}
      <Section
        id="portfolio-hero"
        padding="large"
        background="light"
        className={styles.heroSection}
      >
        <div className={styles.networkBackgroundContainer}>
          <NetworkBackground
            color="#8a2be2"
            shape="portfolio"
            pointCount={80}
            connectionDistance={175}
            baseOpacity={0.4}
            highlightOpacity={0.9}
          />
        </div>
        <div className={styles.portfolioHero}>
          <h1>AI & Full-Stack Development Projects</h1>
          <p>
            Explore a selection of my work across web development, AI projects,
            graphic design, and video production. Each project represents my
            commitment to quality, innovation, and delivering exceptional results.
          </p>
        </div>
      </Section>

      {/* Portfolio Filter */}
      <Section id="portfolio-gallery">
        <div className={styles.portfolioFilter}>
          <button
            className={`${styles.filterButton} ${filter === 'all' ? styles.active : ''}`}
            onClick={() => setFilter('all')}
          >
            All Projects
          </button>
          <button
            className={`${styles.filterButton} ${filter === 'web' ? styles.active : ''}`}
            onClick={() => setFilter('web')}
          >
            Web Apps
          </button>
          <button
            className={`${styles.filterButton} ${filter === 'ai' ? styles.active : ''}`}
            onClick={() => setFilter('ai')}
          >
            AI Projects
          </button>
          <button
            className={`${styles.filterButton} ${filter === 'design' ? styles.active : ''}`}
            onClick={() => setFilter('design')}
          >
            Graphic Design
          </button>
          <button
            className={`${styles.filterButton} ${filter === 'video' ? styles.active : ''}`}
            onClick={() => setFilter('video')}
          >
            Video
          </button>
        </div>

        <div className={styles.portfolioGrid}>
          {filteredProjects.map(project => (
            <div
              key={project.id}
              className={styles.portfolioItem}
              onClick={() => openProjectModal(project)}
            >
              <div className={styles.portfolioItemImage}>
                <img src={project.image} alt={project.altText} />
              </div>
              <div className={styles.portfolioItemOverlay}>
                <h3>{project.title}</h3>
                <p>{project.category.charAt(0).toUpperCase() + project.category.slice(1)}</p>
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Project Modal */}
      {selectedProject && (
        <div className={styles.projectModal}>
          <div className={styles.modalOverlay} onClick={closeProjectModal}></div>
          <div className={styles.modalContent}>
            {/* Removed close button as requested */}

            <div className={styles.modalImage}>
              <img src={selectedProject.image} alt={`${selectedProject.altText} - Detailed project view and technical specifications`} />
            </div>

            <div className={styles.modalDetails}>
              <h2>{selectedProject.title}</h2>
              <p className={styles.modalCategory}>
                {selectedProject.category.charAt(0).toUpperCase() + selectedProject.category.slice(1)}
              </p>

              <div className={styles.modalDescription}>
                <p>{selectedProject.description}</p>
              </div>

              <div className={styles.modalTechnologies}>
                <h3>Technologies Used</h3>
                <div className={styles.techTags}>
                  {selectedProject.technologies.map((tech, index) => (
                    <span key={index} className={styles.techTag}>{tech}</span>
                  ))}
                </div>
              </div>

              <div className={styles.modalActions}>
                <Button
                  href={selectedProject.link}
                  variant="primary"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <FiExternalLink /> View Project
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Technology Stack Section */}
      <Section
        id="technology-stack"
        background="light"
        centered
      >
        <div className={styles.technologyStack}>
          <h2>Modern Tech Stack & AI Platform Integration</h2>
          <p>
            My portfolio showcases projects built with cutting-edge technologies and AI platforms. From React and Node.js
            applications to ChatGPT integrations and Microsoft Copilot implementations, each project demonstrates my
            commitment to using the best tools for optimal results.
          </p>
          <p>
            Whether developing custom AI solutions for education or building full-stack applications with modern frameworks,
            I leverage technologies like TypeScript, Electron, and Gemini Google to create scalable, maintainable solutions
            that drive business growth and user engagement.
          </p>
          <p>
            Each project in my portfolio represents a unique challenge solved through strategic technology selection and
            innovative implementation approaches. From AI in education platforms to enterprise-level applications,
            my work demonstrates the power of combining technical expertise with creative problem-solving.
          </p>
        </div>
      </Section>

      {/* CTA Section */}
      <Section
        id="cta"
        padding="large"
        background="none"
        className={styles.ctaSection}
      >
        <div className={styles.cta}>
          <h2>Have a project in mind?</h2>
          <p>
            I'm always open to discussing new projects and creative ideas.
            If you have a project that needs expertise in development, design,
            or strategy, I'd love to hear from you.
          </p>
          <Button to="/contact" variant="primary" size="large">
            Start a Conversation
          </Button>
        </div>
      </Section>
    </div>
  );
};

export default Portfolio;
