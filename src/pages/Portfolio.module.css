.portfolio {
  width: 100%;
}

/* Portfolio Hero Section */
.heroSection {
  position: relative;
  overflow: hidden;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.networkBackgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.portfolioHero {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.portfolioHero h1 {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-lg);
}

.portfolioHero p {
  font-size: var(--fs-lg);
  line-height: 1.7;
}

/* Technology Stack Section */
.technologyStack {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.technologyStack h2 {
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-primary);
}

.technologyStack p {
  margin-bottom: var(--spacing-md);
  font-size: var(--fs-lg);
  line-height: 1.7;
  color: var(--color-text-secondary);
  text-align: left;
}

/* Portfolio Filter */
.portfolioFilter {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.filterButton {
  background-color: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--fs-sm);
  font-weight: var(--fw-medium);
}

.filterButton:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
  transform: translateY(0);
}

.filterButton.active {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

/* Portfolio Grid */
.portfolioGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.portfolioItem {
  position: relative;
  border-radius: var(--radius-lg);
  overflow: hidden;
  cursor: pointer;
  height: 250px;
  box-shadow: var(--shadow-md);
  transition: transform var(--transition-normal);
}

.portfolioItem:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.portfolioItemImage {
  width: 100%;
  height: 100%;
}

.portfolioItemImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.portfolioItem:hover .portfolioItemImage img {
  transform: scale(1.05);
}

.portfolioItemOverlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: var(--spacing-lg);
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3), transparent);
  color: white;
  transition: opacity var(--transition-normal);
}

.portfolioItemOverlay h3 {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--fw-bold);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.portfolioItemOverlay p {
  font-size: var(--fs-sm);
  opacity: 1;
  font-weight: var(--fw-medium);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
}

/* Project Modal */
.projectModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.modalOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
}

.modalContent {
  position: relative;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  overflow: hidden;
  display: grid;
  grid-template-columns: 1fr 1fr;
  z-index: 1;
}

.modalClose {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: background-color var(--transition-fast);
}

.modalClose:hover {
  background-color: rgba(0, 0, 0, 0.7);
  transform: translateY(0);
}

.modalImage {
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-background-alt);
}

.modalImage img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* Changed from cover to contain to maintain aspect ratio */
  max-height: 90vh;
}

.modalDetails {
  padding: var(--spacing-xl);
  overflow-y: auto;
  max-height: 90vh;
}

.modalDetails h2 {
  font-size: var(--fs-2xl);
  margin-bottom: var(--spacing-xs);
}

.modalCategory {
  color: var(--color-primary);
  font-weight: var(--fw-medium);
  margin-bottom: var(--spacing-lg);
}

.modalDescription {
  margin-bottom: var(--spacing-lg);
}

.modalDescription p {
  font-size: var(--fs-md);
  line-height: 1.7;
}

.modalTechnologies {
  margin-bottom: var(--spacing-xl);
}

.modalTechnologies h3 {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-md);
}

.techTags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.techTag {
  background-color: var(--color-background-alt);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--fs-sm);
  font-weight: var(--fw-medium);
}

.modalActions {
  display: flex;
  gap: var(--spacing-md);
}

/* CTA Section */
.ctaSection {
  background-color: var(--color-background);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.ctaSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.cta h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.cta p {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-light);
}

.cta button {
  position: relative;
  overflow: hidden;
  font-weight: var(--fw-bold);
  letter-spacing: 0.5px;
  padding: calc(var(--spacing-md) + 2px) calc(var(--spacing-xl) + 5px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.cta button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .portfolioGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .modalContent {
    grid-template-columns: 1fr;
    max-height: 80vh;
    overflow-y: auto;
  }

  .modalImage {
    height: auto;
    max-height: 40vh;
  }

  .modalImage img {
    max-height: 40vh;
  }
}

@media (max-width: 768px) {
  .portfolioHero h1 {
    font-size: var(--fs-3xl);
  }

  .portfolioGrid {
    grid-template-columns: 1fr;
  }

  .portfolioFilter {
    flex-direction: column;
    align-items: center;
  }

  .filterButton {
    width: 100%;
    max-width: 300px;
  }
}
