import { FiCpu, FiCode, FiBriefcase, FiPenTool, FiVideo, FiLayout, FiBook, FiUsers } from 'react-icons/fi';
import Section from '../components/ui/Section';
import Button from '../components/ui/Button';
import NetworkBackground from '../components/ui/NetworkBackground';
import styles from './Services.module.css';

const Services = () => {
  const services = [
    {
      id: 'ai-development',
      title: 'AI Development & Strategy',
      icon: <FiCpu />,
      description: 'Transform your business with custom AI solutions leveraging ChatGPT, Gemini Google, and Microsoft Copilot technologies for measurable results.',
      problemStatement: 'Many businesses struggle to harness AI\'s potential due to complex implementation challenges, lack of strategic direction, and difficulty integrating AI tools like ChatGPT and Gemini Google into existing workflows, resulting in missed opportunities for automation and competitive advantage.',
      uniqueApproach: 'My AI-first methodology combines strategic consulting with hands-on implementation, focusing on practical AI in education and business applications. I specialize in creating custom AI solutions that seamlessly integrate with your existing systems while providing comprehensive training on ChatGPT, Gemini Google, and Microsoft Copilot optimization.',
      offerings: [
        'Custom AI Model Development & Training',
        'ChatGPT & Gemini Google Integration',
        'Microsoft Copilot Implementation',
        'AI in Education Solutions',
        'Machine Learning Implementation',
        'Natural Language Processing Systems',
        'Computer Vision Solutions',
        'AI Strategy Consulting & Roadmapping',
        'AI Integration with Existing Systems',
        'Intelligent Automation Workflows'
      ],
      technologyStack: {
        aiPlatforms: [
          'OpenAI GPT Models (ChatGPT, GPT-4)',
          'Google Gemini & Bard AI',
          'Microsoft Copilot Suite',
          'Hugging Face Transformers'
        ],
        mlFrameworks: [
          'TensorFlow',
          'PyTorch',
          'LangChain',
          'LlamaIndex'
        ],
        programmingLanguages: [
          'Python',
          'R for Data Science',
          'JavaScript/TypeScript'
        ],
        cloudPlatforms: [
          'Azure AI Services',
          'Google Cloud AI',
          'AWS SageMaker',
          'OpenAI API'
        ],
        developmentTools: [
          'Jupyter Notebooks',
          'Docker',
          'Git & Version Control',
          'Custom API Development'
        ]
      },
      benefits: [
        'Reduce operational costs by 30-50% through intelligent automation',
        'Increase productivity by 40-60% with AI-powered workflows',
        'Gain competitive advantage with cutting-edge AI in education tools',
        'Improve decision-making with data-driven AI insights',
        'Enhance customer experience through personalized AI interactions',
        'Accelerate time-to-market for AI-powered products',
        'Achieve measurable ROI within 3-6 months of implementation'
      ]
    },
    {
      id: 'full-stack-development',
      title: 'Full-Stack Development',
      icon: <FiCode />,
      description: 'Comprehensive full-stack development services creating scalable web and mobile applications with modern technologies and industry best practices.',
      problemStatement: 'Organizations often face challenges with fragmented development teams, inconsistent technology stacks, lengthy development cycles, and applications that don\'t scale effectively, leading to increased costs, delayed launches, and poor user experiences.',
      uniqueApproach: 'My full-stack development methodology emphasizes rapid prototyping, agile development, and future-proof architecture. I leverage modern frameworks and cloud-native solutions to deliver applications that are not only functional but also scalable, maintainable, and optimized for performance across all devices.',
      offerings: [
        'Modern Web Application Development',
        'Progressive Web Apps (PWA)',
        'Mobile App Development (React Native)',
        'Cross-Platform Solutions',
        'RESTful API Development & Integration',
        'GraphQL Implementation',
        'Database Design & Optimization',
        'Cloud Infrastructure & DevOps',
        'E-commerce Platform Development',
        'Custom CMS Solutions',
        'Real-time Applications',
        'Research & Rapid Prototyping'
      ],
      technologyStack: {
        frontendFrameworks: [
          'React',
          'Vue.js',
          'Next.js',
          'TypeScript'
        ],
        backendRuntime: [
          'Node.js',
          'Express.js',
          'NestJS',
          'RESTful APIs'
        ],
        mobileFrameworks: [
          'React Native',
          'Kotlin',
          'Cross-Platform Solutions'
        ],
        databases: [
          'PostgreSQL',
          'MongoDB',
          'Redis',
          'Database Design & Optimization'
        ],
        cloudPlatforms: [
          'AWS',
          'Google Cloud Platform',
          'Microsoft Azure',
          'Serverless Architecture'
        ],
        devOpsTools: [
          'Docker',
          'Kubernetes',
          'CI/CD Pipelines',
          'Git & Version Control'
        ]
      },
      developmentTools: {
        buildTools: [
          'Webpack',
          'Vite',
          'ESLint',
          'Prettier'
        ],
        testingFrameworks: [
          'Jest',
          'Cypress',
          'Playwright',
          'Unit & Integration Testing'
        ],
        apiTechnologies: [
          'REST APIs',
          'GraphQL',
          'WebSocket',
          'API Documentation'
        ],
        authentication: [
          'OAuth 2.0',
          'JWT Tokens',
          'Auth0',
          'Security Best Practices'
        ]
      },
      benefits: [
        'Reduce development time by 40-50% with proven frameworks',
        'Achieve 99.9% uptime with robust cloud architecture',
        'Increase user engagement by 35-60% with optimized UX',
        'Lower maintenance costs by 30-40% with clean code practices',
        'Accelerate time-to-market with agile development approach',
        'Ensure scalability to handle 10x traffic growth',
        'Improve conversion rates by 25-45% with performance optimization'
      ]
    },
    {
      id: 'tech-trainer',
      title: 'Tech Mentorship',
      icon: <FiBook />,
      description: 'Expert tech mentorship and training programs empowering developers, designers, and entrepreneurs to excel in AI, full-stack development, and digital innovation.',
      problemStatement: 'Many professionals struggle to keep pace with rapidly evolving technology, lack access to personalized guidance, and find it difficult to transition from theoretical knowledge to practical application, resulting in skill gaps and missed career opportunities.',
      uniqueApproach: 'My tech mentorship approach combines personalized learning paths with hands-on project experience and industry insights. I focus on practical application of modern technologies including AI tools, full-stack development, and emerging frameworks, ensuring mentees gain both technical skills and strategic thinking capabilities.',
      offerings: [
        'AI & Machine Learning Mentorship',
        'Full-Stack Development Training (React, Vue, TypeScript)',
        'ChatGPT & AI Tools Mastery',
        'Modern JavaScript & Node.js',
        'Mobile Development (React Native, Kotlin)',
        'Cloud Technologies & DevOps',
        'Digital Design & UX/UI',
        'Entrepreneurship & Tech Strategy',
        '1-on-1 Personalized Mentorship',
        'Corporate Training Programs',
        'Workshops & Intensive Bootcamps',
        'Custom Online Learning Sessions'
      ],
      benefits: [
        'Accelerate career growth by 2-3x with targeted skill development',
        'Increase earning potential by 40-70% through in-demand skills',
        'Gain practical experience with real-world projects and portfolios',
        'Access to industry networks and job placement assistance',
        'Personalized learning paths adapted to your goals and schedule',
        'Ongoing mentorship and career guidance beyond training completion',
        'Stay ahead of technology trends with cutting-edge curriculum'
      ],
      technologyStack: {
        frontendFrameworks: [
          'React',
          'Vue.js',
          'Vite',
          'Astro'
        ],
        backendRuntime: [
          'Node.js'
        ],
        programmingLanguages: [
          'JavaScript',
          'TypeScript',
          'Kotlin'
        ],
        crossPlatform: [
          'Electron (Desktop apps)'
        ]
      },
      designTools: {
        graphicDesign: [
          'Adobe Photoshop',
          'Adobe Illustrator',
          'Figma',
          'Sketch'
        ],
        motionVideoEditing: [
          'Adobe After Effects',
          'Adobe Premiere Pro',
          'DaVinci Resolve'
        ],
        animation3D: [
          'Blender',
          'Cinema 4D'
        ]
      }
    },
    {
      id: 'business-strategy',
      title: 'Business Strategy',
      icon: <FiBriefcase />,
      description: 'Strategic business consulting to help you navigate challenges and capitalize on opportunities with data-driven insights and innovative frameworks.',
      problemStatement: 'Many businesses struggle with unclear strategic direction, ineffective market positioning, and difficulty adapting to digital transformation, resulting in missed growth opportunities, competitive disadvantages, and suboptimal resource allocation that hinders long-term success.',
      uniqueApproach: 'My business strategy methodology combines data-driven market analysis with innovative thinking and practical implementation frameworks. I focus on creating actionable strategies that align with your business goals while leveraging digital transformation opportunities to drive sustainable growth and competitive advantage.',
      offerings: [
        'Market Analysis & Competitive Research',
        'Digital Transformation Strategy',
        'Growth Strategy Development',
        'Product Strategy & Innovation',
        'Business Model Optimization',
        'Strategic Planning & Roadmapping',
        'Performance Metrics & KPI Development',
        'Change Management Consulting',
        'Innovation Framework Development',
        'Strategic Partnership Planning',
        'Risk Assessment & Mitigation',
        'Investment Strategy Consulting'
      ],
      technologyStack: {
        analyticsTools: [
          'Google Analytics',
          'Microsoft Power BI',
          'Tableau',
          'Data Studio'
        ],
        researchPlatforms: [
          'Market Research Tools',
          'Competitive Intelligence',
          'Customer Survey Platforms',
          'Industry Analysis Tools'
        ],
        projectManagement: [
          'Strategic Planning Software',
          'Roadmapping Tools',
          'KPI Dashboards',
          'Performance Tracking'
        ],
        collaborationTools: [
          'Microsoft Office Suite',
          'Google Workspace',
          'Slack',
          'Zoom'
        ]
      },
      consultingTools: {
        frameworksMethodologies: [
          'SWOT Analysis',
          'Porter\'s Five Forces',
          'Blue Ocean Strategy',
          'Lean Canvas'
        ],
        digitalTransformation: [
          'Technology Assessment',
          'Process Automation',
          'Digital Readiness Evaluation',
          'Change Management'
        ],
        performanceMetrics: [
          'OKR Development',
          'KPI Design',
          'ROI Analysis',
          'Performance Dashboards'
        ],
        innovationTools: [
          'Design Thinking',
          'Innovation Workshops',
          'Ideation Sessions',
          'Prototype Development'
        ]
      },
      benefits: [
        'Increase revenue by 25-40% through strategic market positioning',
        'Reduce operational costs by 20-35% with process optimization',
        'Accelerate digital transformation by 50-70% with structured roadmaps',
        'Improve decision-making speed by 40-60% with data-driven insights',
        'Enhance competitive advantage with innovative business models',
        'Achieve strategic goals 2-3x faster with clear implementation plans',
        'Generate measurable ROI within 6-12 months of strategy implementation'
      ]
    },
    {
      id: 'graphic-design',
      title: 'Graphic & Motion Design',
      icon: <FiPenTool />,
      description: 'Creative visual design solutions that strengthen brand identity, enhance user engagement, and communicate your message with compelling graphics and motion.',
      problemStatement: 'Many businesses struggle with inconsistent visual branding, poor graphic design quality, and lack of engaging visual content, resulting in weak brand recognition, reduced customer engagement, and difficulty standing out in competitive markets.',
      uniqueApproach: 'My design methodology combines creative excellence with strategic thinking, focusing on brand consistency and user psychology. I create visually compelling designs that not only look great but also drive engagement, conversions, and brand loyalty through thoughtful visual storytelling.',
      offerings: [
        'Brand Identity Design & Development',
        'Logo Design & Brand Guidelines',
        'Marketing Materials & Collateral',
        'Social Media Graphics & Templates',
        'Motion Graphics & Animations',
        'Infographic Design & Data Visualization',
        'Print Design & Layout',
        'Packaging & Product Design',
        'Presentation Design & Templates',
        'Digital Asset Creation',
        'Icon Design & Illustration',
        'Brand Style Guide Development'
      ],
      technologyStack: {
        designSoftware: [
          'Adobe Creative Suite',
          'Adobe Photoshop',
          'Adobe Illustrator',
          'Adobe InDesign'
        ],
        motionGraphics: [
          'Adobe After Effects',
          'Adobe Premiere Pro',
          'Cinema 4D',
          'Blender'
        ],
        prototypingTools: [
          'Figma',
          'Sketch',
          'Adobe XD',
          'InVision'
        ],
        collaborationPlatforms: [
          'Creative Cloud',
          'Dropbox',
          'Google Drive',
          'Slack'
        ]
      },
      creativeTools: {
        brandingDesign: [
          'Logo Creation',
          'Brand Guidelines',
          'Color Palette Development',
          'Typography Selection'
        ],
        digitalAssets: [
          'Social Media Graphics',
          'Web Graphics',
          'Digital Illustrations',
          'Icon Libraries'
        ],
        printDesign: [
          'Business Cards',
          'Brochures & Flyers',
          'Posters & Banners',
          'Packaging Design'
        ],
        motionDesign: [
          '2D Animation',
          '3D Modeling',
          'Video Graphics',
          'Interactive Elements'
        ]
      },
      benefits: [
        'Increase brand recognition by 40-60% with consistent visual identity',
        'Boost engagement rates by 35-50% with compelling visual content',
        'Improve conversion rates by 25-40% through strategic design',
        'Reduce design costs by 30-45% with efficient design systems',
        'Accelerate marketing campaigns with ready-to-use templates',
        'Enhance professional credibility with high-quality visuals',
        'Achieve brand consistency across all touchpoints and platforms'
      ]
    },
    {
      id: 'video-production',
      title: 'Video Editing & Production',
      icon: <FiVideo />,
      description: 'Professional video editing and production services creating engaging content that tells your story, promotes your brand, and drives audience engagement.',
      problemStatement: 'Many businesses struggle with creating high-quality video content due to lack of technical expertise, expensive equipment requirements, and time-consuming editing processes, resulting in poor video quality, inconsistent branding, and missed opportunities for audience engagement.',
      uniqueApproach: 'My video production methodology combines technical excellence with creative storytelling, focusing on brand consistency and audience engagement. I leverage professional editing techniques and modern tools to create compelling videos that effectively communicate your message and drive results.',
      offerings: [
        'Corporate Video Production',
        'Marketing & Promotional Videos',
        'Social Media Video Content',
        'Educational & Training Videos',
        'Product Demonstration Videos',
        'Event Coverage & Documentation',
        'Motion Graphics & Animations',
        'Video Editing & Post-Production',
        'Color Grading & Audio Enhancement',
        'Video Optimization for Platforms',
        'Live Streaming Setup & Support',
        'Video Content Strategy'
      ],
      technologyStack: {
        editingSoftware: [
          'Adobe Premiere Pro',
          'Adobe After Effects',
          'DaVinci Resolve',
          'Final Cut Pro'
        ],
        motionGraphics: [
          'Adobe After Effects',
          'Cinema 4D',
          'Blender',
          'Motion'
        ],
        audioProduction: [
          'Adobe Audition',
          'Logic Pro',
          'Pro Tools',
          'Audacity'
        ],
        collaborationTools: [
          'Frame.io',
          'Dropbox',
          'Google Drive',
          'Slack'
        ]
      },
      productionTools: {
        videoEquipment: [
          'Professional Cameras',
          'Lighting Equipment',
          'Audio Recording',
          'Stabilization Tools'
        ],
        postProduction: [
          'Color Correction',
          'Audio Mixing',
          'Visual Effects',
          'Motion Tracking'
        ],
        deliveryFormats: [
          'Web Optimization',
          'Social Media Formats',
          'Broadcast Quality',
          'Mobile-First Design'
        ],
        contentStrategy: [
          'Storyboarding',
          'Script Development',
          'Brand Integration',
          'Platform Optimization'
        ]
      },
      benefits: [
        'Increase engagement rates by 50-80% with professional video content',
        'Boost conversion rates by 30-60% through compelling video marketing',
        'Reduce production costs by 40-55% with efficient workflows',
        'Accelerate content creation by 60-75% with streamlined processes',
        'Enhance brand credibility with high-quality video production',
        'Improve social media reach by 3-5x with optimized video content',
        'Achieve measurable ROI within 2-4 months of video campaign launch'
      ]
    },
    {
      id: 'web-ui-ux-design',
      title: 'Web & UI/UX Design',
      icon: <FiLayout />,
      description: 'User-centered design solutions that enhance user experience, drive engagement, and create intuitive digital interfaces that convert visitors into customers.',
      problemStatement: 'Many websites and applications suffer from poor user experience, confusing navigation, and low conversion rates due to inadequate design research, inconsistent interface design, and lack of user-centered thinking, resulting in high bounce rates and lost business opportunities.',
      uniqueApproach: 'My UI/UX design methodology is rooted in user research and data-driven design decisions. I create intuitive, accessible, and conversion-focused designs that not only look beautiful but also solve real user problems and drive business results through strategic design thinking.',
      offerings: [
        'Website Design & Redesign',
        'User Interface (UI) Design',
        'User Experience (UX) Research',
        'Wireframing & Prototyping',
        'Responsive Design Development',
        'Design System Creation',
        'Usability Testing & Analysis',
        'Conversion Rate Optimization',
        'Accessibility Design (WCAG)',
        'Mobile-First Design',
        'E-commerce Design',
        'Landing Page Optimization'
      ],
      technologyStack: {
        designTools: [
          'Figma',
          'Sketch',
          'Adobe XD',
          'InVision'
        ],
        prototypingTools: [
          'Figma Prototyping',
          'Principle',
          'Framer',
          'Marvel'
        ],
        researchTools: [
          'Hotjar',
          'Google Analytics',
          'Maze',
          'UserTesting'
        ],
        collaborationPlatforms: [
          'Figma',
          'Miro',
          'Slack',
          'Notion'
        ]
      },
      designTools: {
        userResearch: [
          'User Interviews',
          'Surveys & Questionnaires',
          'Persona Development',
          'Journey Mapping'
        ],
        designProcess: [
          'Information Architecture',
          'Wireframing',
          'Visual Design',
          'Interactive Prototypes'
        ],
        testingValidation: [
          'Usability Testing',
          'A/B Testing',
          'Heuristic Evaluation',
          'Accessibility Audits'
        ],
        deliverables: [
          'Design Systems',
          'Style Guides',
          'Component Libraries',
          'Design Documentation'
        ]
      },
      benefits: [
        'Increase conversion rates by 40-70% with optimized user experience',
        'Reduce bounce rates by 35-55% through intuitive design',
        'Improve user satisfaction by 50-80% with user-centered design',
        'Accelerate development by 30-50% with comprehensive design systems',
        'Enhance accessibility compliance and reach broader audiences',
        'Boost mobile engagement by 60-90% with responsive design',
        'Achieve design consistency across all digital touchpoints'
      ]
    }
  ];

  // Testimonials section disabled for future use
  /*
  const testimonials = [
    {
      quote: "Hrishikesh's expertise in AI development transformed our business processes, resulting in significant efficiency gains and cost savings.",
      author: "Sarah Johnson",
      company: "Tech Innovations Inc.",
      service: "AI Development"
    },
    {
      quote: "The web application Hrishikesh built for us exceeded our expectations in both functionality and design. Our users love it!",
      author: "Michael Chen",
      company: "Global Solutions",
      service: "Full-Stack Development"
    },
    {
      quote: "Working with Hrishikesh on our brand strategy gave us clarity and direction. His insights were invaluable to our growth.",
      author: "Emily Rodriguez",
      company: "Startup Ventures",
      service: "Business Strategy"
    }
  ];
  */

  return (
    <div className={styles.services}>
      {/* Hero Section */}
      <Section
        id="services-hero"
        padding="large"
        background="light"
        className={styles.heroSection}
      >
        <div className={styles.networkBackgroundContainer}>
          <NetworkBackground
            color="#0070f3"
            shape="services"
            pointCount={80}
            connectionDistance={175}
            baseOpacity={0.4}
            highlightOpacity={0.9}
          />
        </div>
        <div className={styles.servicesHero}>
          <h1>Comprehensive AI & Digital Solutions Services</h1>
          <p>
            Expert AI in education solutions, full-stack development, business strategy, creative design, and tech mentorship services.
            Leveraging ChatGPT, Gemini Google, and Microsoft Copilot technologies to transform businesses with
            measurable results. From AI strategy consulting to video production and UI/UX design,
            I deliver end-to-end digital solutions tailored to your specific goals.
          </p>
        </div>
      </Section>

      {/* Services Sections */}
      {services.map((service, index) => (
        <Section
          key={service.id}
          id={service.id}
          background={index % 2 === 0 ? 'none' : 'light'}
        >
          <div className={styles.serviceSection}>
            <div className={styles.serviceHeader}>
              <div className={styles.serviceIcon}>{service.icon}</div>
              <h2>{service.title}</h2>
              <p className={styles.serviceDescription}>{service.description}</p>
            </div>

            {/* Problem Statement Section */}
            {service.problemStatement && (
              <div className={styles.problemStatement}>
                <h3>The Challenge</h3>
                <p>{service.problemStatement}</p>
              </div>
            )}

            {/* Unique Approach Section */}
            {service.uniqueApproach && (
              <div className={styles.uniqueApproach}>
                <h3>My Unique Approach</h3>
                <p>{service.uniqueApproach}</p>
              </div>
            )}

            <div className={styles.serviceDetails}>
              <div className={styles.serviceOfferings}>
                <h3>What I Offer</h3>
                <ul className={styles.serviceList}>
                  {service.offerings.map((offering, i) => (
                    <li key={i}>{offering}</li>
                  ))}
                </ul>
              </div>

              <div className={styles.serviceBenefits}>
                <h3>Client Benefits & ROI</h3>
                <ul className={styles.serviceList}>
                  {service.benefits.map((benefit, i) => (
                    <li key={i}>{benefit}</li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Technology Stack Section - For all services */}
            {service.technologyStack && (
              <>
                <div className={styles.techStackHeader}>
                  <h3>
                    {service.id === 'tech-trainer'
                      ? 'Technologies & Tools I Teach'
                      : 'Technologies & Tools Used'
                    }
                  </h3>
                  <p>
                    {service.id === 'tech-trainer'
                      ? 'Comprehensive training across modern development and design technologies'
                      : service.id === 'ai-development'
                      ? 'Cutting-edge AI platforms and machine learning technologies'
                      : 'Modern full-stack development technologies and frameworks'
                    }
                  </p>
                </div>
                <div className={`${styles.serviceDetails} ${styles.techStackSection}`}>
                  <div className={styles.serviceOfferings}>
                    <h3>
                      {service.id === 'ai-development' ? 'AI & ML Technologies' : 'Technology Stack'}
                    </h3>

                    {/* AI Development Categories */}
                    {service.id === 'ai-development' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>AI Platforms & Models</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.aiPlatforms.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>ML Frameworks & Libraries</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.mlFrameworks.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Programming Languages</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.programmingLanguages.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Cloud AI Platforms</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.cloudPlatforms.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Development Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.developmentTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Full-Stack Development Categories */}
                    {service.id === 'full-stack-development' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>Frontend & Frameworks</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.frontendFrameworks.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Backend & Runtime</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.backendRuntime.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Mobile Development</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.mobileFrameworks.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Databases</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.databases.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Cloud Platforms</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.cloudPlatforms.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>DevOps & Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.devOpsTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Tech Mentorship Categories */}
                    {service.id === 'tech-trainer' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>Frontend & Frameworks</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.frontendFrameworks.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Backend & Runtime</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.backendRuntime.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Programming Languages</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.programmingLanguages.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Cross-Platform Development</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.crossPlatform.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Business Strategy Categories */}
                    {service.id === 'business-strategy' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>Analytics Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.analyticsTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Research Platforms</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.researchPlatforms.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Project Management</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.projectManagement.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Collaboration Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.collaborationTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Graphic Design Categories */}
                    {service.id === 'graphic-design' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>Design Software</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.designSoftware.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Motion Graphics</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.motionGraphics.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Prototyping Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.prototypingTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Collaboration Platforms</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.collaborationPlatforms.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Video Production Categories */}
                    {service.id === 'video-production' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>Editing Software</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.editingSoftware.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Motion Graphics</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.motionGraphics.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Audio Production</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.audioProduction.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Collaboration Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.collaborationTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Web & UI/UX Design Categories */}
                    {service.id === 'web-ui-ux-design' && (
                      <>
                        <div className={styles.techCategory}>
                          <h4>Design Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.designTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Prototyping Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.prototypingTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Research Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.researchTools.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Collaboration Platforms</h4>
                          <ul className={styles.serviceList}>
                            {service.technologyStack.collaborationPlatforms.map((tech, i) => (
                              <li key={i}>{tech}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}
                  </div>

                  <div className={styles.serviceBenefits}>
                    {/* Full-Stack Development Tools */}
                    {service.id === 'full-stack-development' && service.developmentTools && (
                      <>
                        <h3>Development & Testing Tools</h3>

                        <div className={styles.techCategory}>
                          <h4>Build Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.developmentTools.buildTools.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Testing Frameworks</h4>
                          <ul className={styles.serviceList}>
                            {service.developmentTools.testingFrameworks.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>API Technologies</h4>
                          <ul className={styles.serviceList}>
                            {service.developmentTools.apiTechnologies.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Authentication & Security</h4>
                          <ul className={styles.serviceList}>
                            {service.developmentTools.authentication.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Tech Mentorship Design Tools */}
                    {service.id === 'tech-trainer' && service.designTools && (
                      <>
                        <h3>Design & Creative Tools</h3>

                        <div className={styles.techCategory}>
                          <h4>Graphic Design</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.graphicDesign.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Motion & Video Editing</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.motionVideoEditing.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>3D & Animation</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.animation3D.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* AI Development - Additional section for specialized tools */}
                    {service.id === 'ai-development' && (
                      <>
                        <h3>Specialized AI Tools</h3>

                        <div className={styles.techCategory}>
                          <h4>Data Science & Analytics</h4>
                          <ul className={styles.serviceList}>
                            <li>Pandas & NumPy</li>
                            <li>Scikit-learn</li>
                            <li>Matplotlib & Seaborn</li>
                            <li>Data Preprocessing</li>
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>AI Integration Tools</h4>
                          <ul className={styles.serviceList}>
                            <li>API Integration</li>
                            <li>Webhook Development</li>
                            <li>Real-time Processing</li>
                            <li>Model Deployment</li>
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Business Intelligence</h4>
                          <ul className={styles.serviceList}>
                            <li>Custom Dashboards</li>
                            <li>Automated Reporting</li>
                            <li>Performance Analytics</li>
                            <li>ROI Tracking</li>
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Business Strategy - Consulting Tools */}
                    {service.id === 'business-strategy' && service.consultingTools && (
                      <>
                        <h3>Consulting Frameworks & Tools</h3>

                        <div className={styles.techCategory}>
                          <h4>Frameworks & Methodologies</h4>
                          <ul className={styles.serviceList}>
                            {service.consultingTools.frameworksMethodologies.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Digital Transformation</h4>
                          <ul className={styles.serviceList}>
                            {service.consultingTools.digitalTransformation.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Performance Metrics</h4>
                          <ul className={styles.serviceList}>
                            {service.consultingTools.performanceMetrics.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Innovation Tools</h4>
                          <ul className={styles.serviceList}>
                            {service.consultingTools.innovationTools.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Graphic Design - Creative Tools */}
                    {service.id === 'graphic-design' && service.creativeTools && (
                      <>
                        <h3>Creative Design Specializations</h3>

                        <div className={styles.techCategory}>
                          <h4>Branding & Design</h4>
                          <ul className={styles.serviceList}>
                            {service.creativeTools.brandingDesign.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Digital Assets</h4>
                          <ul className={styles.serviceList}>
                            {service.creativeTools.digitalAssets.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Print Design</h4>
                          <ul className={styles.serviceList}>
                            {service.creativeTools.printDesign.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Motion Design</h4>
                          <ul className={styles.serviceList}>
                            {service.creativeTools.motionDesign.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Video Production - Production Tools */}
                    {service.id === 'video-production' && service.productionTools && (
                      <>
                        <h3>Production & Post-Production</h3>

                        <div className={styles.techCategory}>
                          <h4>Video Equipment</h4>
                          <ul className={styles.serviceList}>
                            {service.productionTools.videoEquipment.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Post Production</h4>
                          <ul className={styles.serviceList}>
                            {service.productionTools.postProduction.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Delivery Formats</h4>
                          <ul className={styles.serviceList}>
                            {service.productionTools.deliveryFormats.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Content Strategy</h4>
                          <ul className={styles.serviceList}>
                            {service.productionTools.contentStrategy.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}

                    {/* Web & UI/UX Design - Design Tools */}
                    {service.id === 'web-ui-ux-design' && service.designTools && (
                      <>
                        <h3>UX/UI Design Process</h3>

                        <div className={styles.techCategory}>
                          <h4>User Research</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.userResearch.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Design Process</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.designProcess.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Testing & Validation</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.testingValidation.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>

                        <div className={styles.techCategory}>
                          <h4>Deliverables</h4>
                          <ul className={styles.serviceList}>
                            {service.designTools.deliverables.map((tool, i) => (
                              <li key={i}>{tool}</li>
                            ))}
                          </ul>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}

            <div className={styles.serviceCta}>
              <Button to="/contact" variant="primary">
                Inquire About {service.title}
              </Button>
            </div>
          </div>
        </Section>
      ))}

      {/* CTA Section */}
      <Section
        id="cta"
        padding="large"
        background="none"
        className={styles.ctaSection}
      >
        <div className={styles.cta}>
          <h2>Ready to get started?</h2>
          <p>
            Let's discuss your project and how I can help you achieve your goals.
            Contact me today for a consultation.
          </p>
          <Button to="/contact" variant="primary" size="large">
            Get in Touch
          </Button>
        </div>
      </Section>
    </div>
  );
};

export default Services;
