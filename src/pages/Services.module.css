.services {
  width: 100%;
}

/* Services Hero Section */
.heroSection {
  position: relative;
  overflow: hidden;
  min-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.networkBackgroundContainer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.servicesHero {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.servicesHero h1 {
  font-size: var(--fs-4xl);
  margin-bottom: var(--spacing-lg);
}

.servicesHero p {
  font-size: var(--fs-lg);
  line-height: 1.7;
}

.locationHighlight {
  margin-top: var(--spacing-md);
  font-size: var(--fs-md);
  font-weight: 500;
  color: var(--color-primary);
  background: linear-gradient(90deg, var(--color-primary-light) 0%, var(--color-primary) 50%, var(--color-primary-light) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 0 5px rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-md);
  display: inline-block;
}

/* Service Section */
.serviceSection {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.serviceHeader {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-xl);
}

.serviceIcon {
  font-size: var(--fs-3xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border-radius: var(--radius-full);
  margin: 0 auto var(--spacing-lg);
}

.serviceHeader h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
}

.serviceDescription {
  font-size: var(--fs-lg);
  color: var(--color-text-light);
}

/* Problem Statement Section */
.problemStatement {
  background-color: rgba(var(--color-primary-rgb), 0.05);
  border-left: 4px solid var(--color-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.problemStatement h3 {
  font-size: var(--fs-xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
}

.problemStatement h3::before {
  content: '⚠️';
  margin-right: var(--spacing-sm);
  font-size: 1.2em;
}

.problemStatement p {
  font-size: var(--fs-md);
  line-height: 1.7;
  color: var(--color-text);
}

/* Unique Approach Section */
.uniqueApproach {
  background-color: rgba(var(--color-accent-rgb), 0.05);
  border-left: 4px solid var(--color-accent);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.uniqueApproach h3 {
  font-size: var(--fs-xl);
  color: var(--color-accent);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
}

.uniqueApproach h3::before {
  content: '💡';
  margin-right: var(--spacing-sm);
  font-size: 1.2em;
}

.uniqueApproach p {
  font-size: var(--fs-md);
  line-height: 1.7;
  color: var(--color-text);
}

/* Technologies Section */
.technologiesSection {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-lg);
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.technologiesSection h3 {
  font-size: var(--fs-xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.technologiesSection h3::before {
  content: '🛠️';
  margin-right: var(--spacing-sm);
  font-size: 1.2em;
}

.technologiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-sm);
}

.techItem {
  background-color: rgba(var(--color-primary-rgb), 0.1);
  border: 1px solid rgba(var(--color-primary-rgb), 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--fs-sm);
  font-weight: var(--fw-medium);
  color: var(--color-text);
  text-align: center;
  transition: all var(--transition-fast);
}

.techItem:hover {
  background-color: rgba(var(--color-primary-rgb), 0.15);
  border-color: var(--color-primary);
  transform: translateY(-2px);
}

.serviceDetails {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
}

.serviceOfferings,
.serviceBenefits {
  background-color: var(--color-background);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.serviceOfferings h3,
.serviceBenefits h3 {
  font-size: var(--fs-xl);
  margin-bottom: var(--spacing-lg);
  color: var(--color-primary);
}

.serviceList {
  list-style: none;
  padding: 0;
}

.serviceList li {
  position: relative;
  padding-left: 30px;
  margin-bottom: var(--spacing-md);
  font-size: var(--fs-md);
}

.serviceList li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--color-primary);
  font-weight: var(--fw-bold);
}

/* Technology Stack Section */
.techStackHeader {
  text-align: center;
  margin: var(--spacing-xl) 0 var(--spacing-lg);
}

.techStackHeader h3 {
  font-size: var(--fs-xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
}

.techStackHeader p {
  font-size: var(--fs-md);
  color: var(--color-text-light);
  max-width: 700px;
  margin: 0 auto;
}

.techStackSection {
  margin-top: var(--spacing-lg);
  position: relative;
}

.techStackSection::before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.techCategory {
  margin-bottom: var(--spacing-lg);
}

.techCategory h4 {
  font-size: var(--fs-md);
  color: var(--color-primary);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--fw-semibold);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
}

.techCategory h4::before {
  content: '•';
  margin-right: 8px;
  color: var(--color-primary);
  font-size: 1.2em;
}

.techCategory:last-child {
  margin-bottom: 0;
}

.serviceCta {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

/* Testimonials Section */
.testimonials {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.testimonialCard {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.testimonialQuote {
  flex: 1;
}

.testimonialQuote p {
  font-size: var(--fs-md);
  line-height: 1.7;
  color: white;
  font-style: italic;
}

.testimonialAuthor {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: var(--spacing-md);
}

.authorName {
  font-weight: var(--fw-semibold);
  color: white;
  margin-bottom: var(--spacing-xs);
}

.authorCompany {
  font-size: var(--fs-sm);
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-xs);
}

.authorService {
  font-size: var(--fs-xs);
  color: var(--color-accent);
  font-weight: var(--fw-medium);
}

/* CTA Section */
.ctaSection {
  background-color: var(--color-background);
  position: relative;
}

.ctaSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.ctaSection::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--color-border), transparent);
}

.cta {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-xl) 0;
}

.cta h2 {
  font-size: var(--fs-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--color-text);
}

.cta p {
  font-size: var(--fs-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--color-text-light);
}

.cta button {
  position: relative;
  overflow: hidden;
  font-weight: var(--fw-bold);
  letter-spacing: 0.5px;
  padding: calc(var(--spacing-md) + 2px) calc(var(--spacing-xl) + 5px);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.cta button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .serviceDetails {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .testimonials {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .techStackSection {
    margin-top: var(--spacing-xl);
  }

  .techCategory {
    margin-bottom: var(--spacing-md);
  }

  .technologiesGrid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xs);
  }

  .problemStatement,
  .uniqueApproach {
    padding: var(--spacing-md);
    margin: var(--spacing-lg) 0;
  }

  .technologiesSection {
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .servicesHero h1 {
    font-size: var(--fs-3xl);
  }

  .serviceHeader h2 {
    font-size: var(--fs-2xl);
  }

  .serviceCta {
    margin-top: var(--spacing-xl);
  }

  .serviceCta a {
    width: auto;
    max-width: 90%;
    text-align: center;
  }

  .technologiesGrid {
    grid-template-columns: 1fr;
    gap: var(--spacing-xs);
  }

  .problemStatement h3,
  .uniqueApproach h3,
  .technologiesSection h3 {
    font-size: var(--fs-lg);
  }

  .problemStatement h3::before,
  .uniqueApproach h3::before,
  .technologiesSection h3::before {
    font-size: 1em;
  }
}

@media (max-width: 480px) {
  .serviceCta a {
    width: 100%;
    max-width: 100%;
  }
}
