/**
 * Website Information Service for the chatbot
 *
 * This service provides methods to retrieve information about the website
 * without relying on external AI APIs.
 */

// Import website information functionality
import { getWebsiteInfo, getExpertiseInfo } from './webSearchService';

// Information about <PERSON><PERSON><PERSON><PERSON><PERSON> for context in responses
const PORTFOLIO_INFO = {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  title: '<PERSON><PERSON><PERSON><PERSON><PERSON>, AI Strategist, Developer, Designer & Tech Mentor',
  company: 'Ajinkya Creatiion',
  expertise: [
    'AI Development & Strategy',
    'Full-Stack Development',
    'Tech Trainer & Mentor',
    'Business Strategy',
    'Graphic Design',
    'UI/UX Design'
  ],
  experience: '10+ years in technology and design',
  author: 'Books on technology, innovation, and entrepreneurship'
};

/**
 * Get a response using website information
 * @param {string} userMessage - The user's message
 * @param {Array} conversationHistory - Previous conversation history
 * @returns {Promise<string>} - The response with website information
 */
export const getAIResponse = async (userMessage, conversationHistory = []) => {
  try {
    // Get relevant website information
    const websiteInfo = await getWebsiteInfo(userMessage);

    // Check if the query is about a specific expertise area
    const expertiseInfo = await getExpertiseInfo(userMessage);

    // Generate a response based on website information
    return await generateWebsiteResponse(userMessage, websiteInfo, expertiseInfo, conversationHistory);
  } catch (error) {
    console.error('Error in website information service:', error);

    // Fallback to simulated response if website information retrieval fails
    return await simulateAIResponse(userMessage, conversationHistory);
  }
};

/**
 * Generate a response based on website information
 * @param {string} userMessage - The user's message
 * @param {Array} websiteInfo - Relevant website sections
 * @param {Object|null} expertiseInfo - Information about a specific expertise area
 * @param {Array} conversationHistory - Previous conversation history
 * @returns {Promise<string>} - The generated response
 */
const generateWebsiteResponse = async (userMessage, websiteInfo, expertiseInfo, conversationHistory) => {
  const normalizedMessage = userMessage.toLowerCase();

  // If we have information about a specific expertise area
  if (expertiseInfo) {
    const detailsList = expertiseInfo.details.map(d => `• ${d}`).join('\n');
    return `**${expertiseInfo.title}**\n\n${expertiseInfo.description}\n\nServices include:\n${detailsList}\n\nWould you like to know more about ${expertiseInfo.title} or discuss a potential project?`;
  }

  // If we have relevant website sections
  if (websiteInfo && websiteInfo.length > 0) {
    // Format website sections into a readable response
    const formattedSections = websiteInfo.map(section => {
      return `**${section.title}**\n${section.description}\n[Visit ${section.title} page](${section.path})`;
    }).join('\n\n');

    // For queries about information
    if (normalizedMessage.includes('what') ||
        normalizedMessage.includes('how') ||
        normalizedMessage.includes('tell me about')) {
      return `Here's information from the website that might help you:\n\n${formattedSections}\n\nIs there anything specific you'd like to know more about?`;
    }

    // For navigation queries
    if (normalizedMessage.includes('find') ||
        normalizedMessage.includes('where') ||
        normalizedMessage.includes('show me')) {
      return `You can find what you're looking for in these sections of the website:\n\n${formattedSections}\n\nClick on any of the links to navigate to that page.`;
    }

    // For general queries
    return `Here's what I found on the website that might help you:\n\n${formattedSections}\n\nIs this what you were looking for?`;
  }

  // If no relevant information was found, fall back to simulated response
  return await simulateAIResponse(userMessage, conversationHistory);
};

// This function was previously used but is now commented out as it's not currently needed
// If needed in the future, it can be uncommented and used
/*
const extractMainTopic = (message) => {
  // Remove common question phrases
  let cleanedMessage = message.toLowerCase()
    .replace(/what is|how to|explain|tell me about|i want to know about|where is|find|show me/g, '')
    .trim();

  // Check for website-specific keywords first
  const websiteKeywords = {
    'services': ['service', 'offer', 'provide', 'help with', 'do you do'],
    'portfolio': ['portfolio', 'project', 'work', 'case study', 'example'],
    'about': ['about', 'background', 'experience', 'bio', 'who is'],
    'contact': ['contact', 'email', 'reach', 'touch', 'connect'],
    'books': ['book', 'author', 'writing', 'read', 'publication']
  };

  for (const [topic, keywords] of Object.entries(websiteKeywords)) {
    for (const keyword of keywords) {
      if (message.toLowerCase().includes(keyword)) {
        return topic;
      }
    }
  }

  // Check for expertise areas
  const expertiseKeywords = {
    'ai': ['ai', 'artificial intelligence', 'machine learning', 'ml', 'deep learning'],
    'web development': ['web', 'website', 'development', 'programming', 'coding', 'full-stack'],
    'tech training': ['training', 'mentor', 'teach', 'learn', 'education', 'course', 'workshop', 'bootcamp', 'class'],
    'design': ['design', 'ui', 'ux', 'user interface', 'user experience', 'graphic'],
    'business': ['business', 'strategy', 'consulting', 'transformation', 'growth']
  };

  for (const [topic, keywords] of Object.entries(expertiseKeywords)) {
    for (const keyword of keywords) {
      if (message.toLowerCase().includes(keyword)) {
        return topic;
      }
    }
  }

  // If no specific keywords were found, extract potential topics
  const words = cleanedMessage.split(/\s+/);

  // If we have a short phrase (1-3 words), it's likely the topic
  if (words.length <= 3) {
    return cleanedMessage;
  }

  // For longer phrases, try to extract key nouns
  const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for'];
  const potentialTopics = words.filter(word =>
    word.length > 3 && !commonWords.includes(word)
  );

  if (potentialTopics.length > 0) {
    // Return the first 2-3 potential topic words
    return potentialTopics.slice(0, Math.min(3, potentialTopics.length)).join(' ');
  }

  // Fallback to the first few words if we couldn't extract a clear topic
  return words.slice(0, Math.min(3, words.length)).join(' ');
};
*/

/**
 * Simulate an AI response when web search fails or for basic queries
 * @param {string} userMessage - The user's message
 * @param {Array} conversationHistory - Previous conversation history
 * @returns {Promise<string>} - The simulated response
 */
const simulateAIResponse = async (userMessage, conversationHistory = []) => {
  const normalizedMessage = userMessage.toLowerCase();

  // For queries about trends or recent information
  if (normalizedMessage.includes('latest') ||
      normalizedMessage.includes('news') ||
      normalizedMessage.includes('recent') ||
      normalizedMessage.includes('trends')) {
    return `Based on recent information, ${generateTrendResponse(normalizedMessage)}`;
  }

  // For educational or explanatory questions
  if (normalizedMessage.includes('how to') ||
      normalizedMessage.includes('what is') ||
      normalizedMessage.includes('explain')) {
    return generateExplanationResponse(normalizedMessage);
  }

  // For other queries, use context-aware responses
  return generateContextAwareResponse(userMessage, conversationHistory);
};

/**
 * Generate a response about trends or recent information
 * @param {string} message - The user's message
 * @returns {string} - A response about trends
 */
const generateTrendResponse = (message) => {
  const topics = {
    'ai': 'AI technology is rapidly evolving with multimodal models becoming more prevalent. Large language models like GPT-4 are being integrated into various business applications, and there is growing focus on responsible AI development.',
    'web': 'Web development trends include the rise of Jamstack architectures, increased adoption of WebAssembly, and the growing popularity of frameworks like Next.js and Astro that offer hybrid rendering approaches.',
    'training': 'Tech training and mentorship is evolving with increased demand for AI skills, personalized learning paths, project-based approaches, and hybrid in-person/remote formats. There\'s growing recognition of the importance of soft skills alongside technical knowledge.',
    'design': 'Current design trends include glassmorphism, 3D elements in interfaces, dark mode optimization, and microinteractions that enhance user experience.',
    'business': 'Recent business trends include the expansion of remote work policies, increased focus on sustainability, and the integration of AI tools into workflow optimization.',
    'mobile': 'Mobile development is seeing a shift toward cross-platform frameworks like React Native and Flutter, along with increased focus on performance optimization and accessibility.'
  };

  // Determine which topic the message is most related to
  for (const [topic, response] of Object.entries(topics)) {
    if (message.includes(topic)) {
      return response;
    }
  }

  // Default response if no specific topic is matched
  return 'there are several emerging trends in technology and business. AI integration, sustainability initiatives, and improved user experiences are becoming priorities across industries. Would you like me to focus on a specific area?';
};

/**
 * Generate an explanation response for educational queries
 * @param {string} message - The user's message
 * @returns {string} - An educational response
 */
const generateExplanationResponse = (message) => {
  if (message.includes('ai') || message.includes('artificial intelligence')) {
    return 'Artificial Intelligence (AI) refers to systems designed to perform tasks that typically require human intelligence. These include learning, reasoning, problem-solving, perception, and language understanding. Modern AI often uses machine learning techniques where algorithms improve through experience. Hrishikesh specializes in developing custom AI solutions and strategies for businesses.';
  }

  if (message.includes('full stack') || message.includes('web development')) {
    return 'Full-stack development involves working with both front-end (client-side) and back-end (server-side) technologies. A full-stack developer like Hrishikesh has expertise in HTML/CSS/JavaScript for the front end, as well as server languages, databases, and architecture for the back end. This comprehensive knowledge allows for building complete web applications from start to finish.';
  }

  if (message.includes('ui') || message.includes('ux') || message.includes('user experience')) {
    return 'UI (User Interface) design focuses on the visual elements users interact with, while UX (User Experience) design addresses the overall feel of the interaction. Good UI/UX design creates intuitive, efficient, and enjoyable experiences for users. Hrishikesh combines both aspects in his design work to create engaging digital products.';
  }

  if (message.includes('training') || message.includes('mentor') || message.includes('teach') || message.includes('learn')) {
    return 'Tech training and mentorship involves guiding individuals or teams in developing technical skills through structured learning experiences. As a Tech Trainer & Mentor, Hrishikesh offers personalized instruction across various technology stacks (React, Vue.js, TypeScript, Node.js, etc.), design tools (Adobe Creative Suite, Figma, Blender), and AI concepts. His approach emphasizes hands-on, project-based learning that builds both technical proficiency and problem-solving abilities, available through 1-on-1 mentorship, workshops, corporate training, and customized online classes.';
  }

  // Default educational response
  return "That's an interesting question. While I don't have specific information on that exact topic, I can tell you that Hrishikesh has extensive experience across multiple domains including AI development, full-stack programming, tech training & mentorship, and digital design. He approaches problems with both technical expertise and creative thinking. Would you like to know more about any of these areas?";
};

/**
 * Generate a context-aware response based on conversation history
 * @param {string} message - The user's message
 * @param {Array} history - Previous conversation history
 * @returns {string} - A context-aware response
 */
const generateContextAwareResponse = (message, history) => {
  const normalizedMessage = message.toLowerCase();

  // Check for follow-up questions
  if (history.length > 0) {
    const lastBotMessage = history.filter(m => m.role === 'assistant').pop()?.content || '';

    // If the last bot message mentioned services and user is asking for more details
    if (lastBotMessage.includes('services') &&
        (normalizedMessage.includes('more') || normalizedMessage.includes('details') || normalizedMessage.includes('tell me about'))) {
      return 'Hrishikesh offers comprehensive services in several areas:\n\n• **AI Development**: Custom AI solutions, machine learning implementation, and AI strategy consulting\n\n• **Full-Stack Development**: Web and mobile applications, APIs, databases, and cloud infrastructure\n\n• **Tech Trainer & Mentor**: Personalized training and mentorship in development, design, and AI technologies\n\n• **Business Strategy**: Market analysis, growth planning, and digital transformation\n\n• **Design Services**: UI/UX design, branding, and visual communication\n\nEach service is tailored to the specific needs of clients. Would you like more information about any specific service?';
    }

    // If discussing projects and user asks about examples
    if ((lastBotMessage.includes('project') || lastBotMessage.includes('portfolio')) &&
        (normalizedMessage.includes('example') || normalizedMessage.includes('like what') || normalizedMessage.includes('such as'))) {
      return 'Hrishikesh has worked on diverse projects including:\n\n• An AI-powered content recommendation system for a digital publishing platform\n\n• A full-stack e-commerce solution with integrated payment processing\n\n• Strategic digital transformation consulting for traditional businesses\n\n• Brand identity and UI/UX design for startups\n\nYou can explore his complete portfolio on the website for more detailed case studies.';
    }
  }

  // If no specific context is detected, use a more general response
  if (normalizedMessage.includes('cost') || normalizedMessage.includes('price') || normalizedMessage.includes('charge')) {
    return "Pricing for Hrishikesh's services varies based on project scope, complexity, and timeline. He offers customized solutions tailored to each client's specific needs and budget. For a detailed quote, you can reach out through the contact form on this website.";
  }

  if (normalizedMessage.includes('process') || normalizedMessage.includes('work with') || normalizedMessage.includes('collaborate')) {
    return "Hrishikesh's work process typically involves:\n\n1. **Discovery**: Understanding your goals, challenges, and requirements\n\n2. **Strategy**: Developing a tailored approach to address your specific needs\n\n3. **Execution**: Implementing the solution with regular updates and feedback\n\n4. **Refinement**: Iterating based on results and evolving requirements\n\n5. **Support**: Providing ongoing assistance and maintenance as needed\n\nThis collaborative approach ensures that the final deliverables align perfectly with your vision and objectives.";
  }

  // Default response for other queries
  return "I understand you're interested in learning more about Hrishikesh's expertise and services. He combines technical knowledge with creative thinking to deliver innovative solutions across AI development, web applications, tech training & mentorship, business strategy, and design. Is there a specific aspect of his work you'd like to explore further?";
};
