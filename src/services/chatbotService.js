/**
 * Chatbot service for handling user interactions
 *
 * This service provides methods to process user messages and generate responses
 * using web search functionality without relying on external AI APIs.
 */

// Import website information service
import { getWebsiteInfo, getExpertiseInfo } from './webSearchService';

// Enhanced knowledge base with contextual information for more intelligent responses
const CHATBOT_KNOWLEDGE = {
  services: [
    {
      name: 'AI Development & Strategy',
      description: 'Custom AI solutions and strategic consulting to transform your business with cutting-edge technology.',
      details: [
        'Custom AI Model Development',
        'Machine Learning Implementation',
        'Natural Language Processing',
        'Computer Vision Solutions',
        'AI Strategy Consulting',
        'AI Integration with Existing Systems'
      ],
      context: {
        expertise: '<PERSON><PERSON><PERSON><PERSON><PERSON> has implemented AI solutions for various industries including healthcare, finance, and e-commerce, focusing on practical applications that deliver measurable business value.',
        approach: 'He takes a business-first approach to AI, ensuring solutions address real problems rather than implementing technology for its own sake.',
        ideal_for: 'Businesses looking to leverage AI for automation, insights, or enhancing customer experiences.',
        case_study: 'Recently developed a custom NLP solution that improved customer service response times by 40% for a mid-sized e-commerce company.'
      }
    },
    {
      name: 'Full-Stack Development',
      description: 'End-to-end web and mobile application development with modern technologies and best practices.',
      details: [
        'Web Application Development',
        'Mobile App Development',
        'API Development',
        'Database Design & Implementation',
        'Cloud Infrastructure Setup',
        'DevOps & CI/CD Integration'
      ],
      context: {
        expertise: 'Hrishikesh specializes in React, Vue, Node.js, and TypeScript, with experience building scalable applications from small business websites to enterprise-level platforms.',
        approach: 'He emphasizes clean code architecture, performance optimization, and future-proof design patterns.',
        ideal_for: 'Startups and established businesses needing custom software solutions that can scale with their growth.',
        case_study: 'Developed a full-stack e-learning platform that supports 10,000+ concurrent users with real-time features and comprehensive analytics.'
      }
    },
    {
      name: 'Tech Trainer & Mentor',
      description: 'Empowering aspiring developers, designers, and entrepreneurs with the knowledge and skills needed to excel in the digital landscape.',
      details: [
        'Full-Stack Development (React, Vite, TypeScript, Kotlin, Vue, Electron, Astro)',
        'AI & Automation (AI agents, LLMs, prompt engineering, intelligent workflows)',
        'Digital Design & Content Creation (Graphic design, video editing, 3D modeling)',
        'Entrepreneurship & Tech Strategy (Building products, launching startups, business scaling)',
        '1-on-1 Mentorship Sessions',
        'Workshops & Bootcamps',
        'Corporate Training Programs',
        'Online Classes (Custom Sessions Available)'
      ],
      context: {
        expertise: 'Hrishikesh brings his extensive experience across multiple technology stacks and design tools to provide comprehensive training and mentorship, with a focus on practical, hands-on learning.',
        approach: 'He tailors each training program to the individual or organization, focusing on real-world applications and projects that build both technical skills and problem-solving abilities.',
        ideal_for: 'Aspiring developers, career changers, design professionals looking to expand their technical skills, and organizations seeking to upskill their teams.',
        case_study: 'Mentored a team of junior developers through a 3-month intensive program, resulting in a 70% improvement in code quality metrics and successful deployment of their first production application.'
      }
    },
    {
      name: 'Business Strategy',
      description: 'Strategic business consulting to help you navigate challenges and capitalize on opportunities.',
      details: [
        'Market Analysis',
        'Competitive Research',
        'Growth Strategy',
        'Digital Transformation',
        'Product Strategy',
        'Innovation Consulting'
      ],
      context: {
        expertise: 'With experience founding and growing Ajinkya Creatiion, Hrishikesh brings practical insights to business strategy, particularly for technology-focused companies.',
        approach: 'He combines data-driven analysis with creative thinking to develop strategies that are both innovative and practical.',
        ideal_for: 'Businesses at inflection points, seeking growth, or navigating digital transformation.',
        case_study: 'Helped a traditional retail business develop and implement a digital transformation strategy that increased their revenue by 35% within the first year.'
      }
    },
    {
      name: 'Graphic Design',
      description: 'Creative visual solutions including branding, marketing materials, and digital assets.',
      details: [
        'Brand Identity Design',
        'Logo Design',
        'Marketing Materials',
        'Social Media Graphics',
        'UI/UX Design',
        'Print Design'
      ],
      context: {
        expertise: 'Hrishikesh began his career in graphic design and maintains a strong aesthetic sensibility across all his work, with particular strength in brand identity and digital design.',
        approach: 'He focuses on design that communicates clearly while reinforcing brand values and creating emotional connections with audiences.',
        ideal_for: 'Businesses looking to establish or refresh their visual identity or improve their marketing materials.',
        case_study: 'Created a comprehensive brand identity for a tech startup that helped them secure funding and establish immediate market recognition.'
      }
    },
    {
      name: 'Video Production',
      description: 'Professional video content creation from concept to final delivery.',
      details: [
        'Corporate Videos',
        'Promotional Content',
        'Motion Graphics',
        'Video Editing',
        'Animation',
        'Post-Production'
      ],
      context: {
        expertise: 'Hrishikesh combines technical skill with storytelling ability to create videos that engage viewers and drive action.',
        approach: 'He emphasizes clear messaging, high production value, and content tailored to specific platforms and audience needs.',
        ideal_for: 'Companies looking to enhance their marketing, training, or internal communications with professional video content.',
        case_study: 'Produced a series of product explanation videos that increased conversion rates by 28% for a SaaS company.'
      }
    },
    {
      name: 'Web & UI/UX Design',
      description: 'User-centered design solutions that enhance user experience and drive engagement.',
      details: [
        'Website Design',
        'User Interface Design',
        'User Experience Design',
        'Wireframing & Prototyping',
        'Responsive Design',
        'Design Systems'
      ],
      context: {
        expertise: 'Hrishikesh combines aesthetic design with deep understanding of user psychology and technical implementation considerations.',
        approach: 'He follows a user-centered design process with research, testing, and iteration to create interfaces that are both beautiful and functional.',
        ideal_for: 'Companies looking to improve user satisfaction, increase conversions, or streamline digital interactions.',
        case_study: 'Redesigned an e-commerce website that resulted in a 45% increase in conversion rate and 30% reduction in cart abandonment.'
      }
    }
  ],
  about: {
    name: 'Hrishikesh Mohite',
    title: 'Entrepreneur, AI Strategist, Developer, Designer & Tech Mentor',
    background: 'My journey began as a graphic designer, expanded to video editing and 3D modeling, then to programming (Kotlin, JavaScript, TypeScript, React, Vue, Vite, Electron, Astro), ultimately founding Ajinkya Creatiion where I combine creativity with technology as an entrepreneur, business strategist, AI innovator, and passionate tech mentor.',
    experience: '10+ years of experience in technology and design',
    projects: '100+ projects completed across various domains',
    clients: '50+ happy clients from startups to enterprises',
    philosophy: 'I believe in creating technology that serves human needs and enhances lives rather than technology for its own sake. My interdisciplinary background allows me to bridge the gap between technical possibilities and practical business applications, and I\'m passionate about sharing this knowledge with others through mentorship and training.',
    career_highlights: [
      'Founded Ajinkya Creatiion in 2023, a technology company focused on AI solutions and digital transformation',
      'Led digital transformation initiatives for multiple mid-sized businesses',
      'Developed custom AI solutions that delivered measurable ROI for clients',
      'Published books on technology, innovation, and entrepreneurship',
      'Established comprehensive tech training programs for individuals and organizations',
      'Mentored emerging developers and designers through structured learning paths'
    ],
    education: 'Self-taught in programming with formal background in design, supplemented by continuous learning through courses, workshops, and hands-on project experience. This diverse learning journey informs my approach to teaching and mentoring others in technology.'
  },
  contact: {
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/hrishikeshmohite',
    website: 'https://ajinkyacreatiion.com',
    instagram: 'https://www.instagram.com/hrishikesh_mohite_/',
    youtube: 'https://www.youtube.com/@kontentcreate',
    response_time: 'Usually responds within 24-48 hours',
    preferred_contact: 'Email is the best way to reach out for initial inquiries, with follow-up calls scheduled as needed.'
  },
  books: {
    topics: ['Technology trends', 'Digital transformation', 'Entrepreneurship', 'Leadership in tech', 'Innovation strategies'],
    audience: 'Business leaders, entrepreneurs, and technology professionals looking to stay ahead of industry changes',
    philosophy: 'My books aim to translate complex technological concepts into practical insights that readers can apply to their businesses and careers.'
  },
  faq: [
    {
      question: 'What types of projects does Hrishikesh typically work on?',
      answer: 'I work on a diverse range of projects including AI implementation, web and mobile application development, business strategy consulting, and creative design. I particularly enjoy projects that combine multiple disciplines, such as developing AI-enhanced web applications or creating comprehensive digital transformation strategies.'
    },
    {
      question: 'How does the project process typically work?',
      answer: 'My process begins with a thorough discovery phase to understand your business needs and objectives. From there, I develop a tailored strategy and implementation plan. For development projects, I follow an agile methodology with regular check-ins and iterations. Throughout the process, I maintain clear communication and focus on delivering measurable value.'
    },
    {
      question: "What makes Hrishikesh's approach different?",
      answer: "My interdisciplinary background allows me to see projects from multiple perspectives - technical, design, business, and user experience. This holistic approach results in solutions that are not only technically sound but also aesthetically pleasing, business-aligned, and user-friendly."
    }
  ]
};

/**
 * Process a user message and generate a contextually aware response
 * @param {string} message - The user's message
 * @param {Array} history - Previous conversation history
 * @returns {Promise<string>} - The response with website information
 */
export const sendMessage = async (message, history = []) => {
  try {
    // First check if we can answer from our knowledge base with context awareness
    const knowledgeBaseResponse = generateKnowledgeBaseResponse(message, history);

    if (knowledgeBaseResponse) {
      return knowledgeBaseResponse;
    }

    // If not in knowledge base, get relevant website information
    const websiteInfo = await getWebsiteInfo(message);

    // Check if the query is about a specific expertise area
    const expertiseInfo = await getExpertiseInfo(message);

    // Generate a response based on website information and conversation history
    const response = await generateWebsiteResponse(message, websiteInfo, expertiseInfo, history);
    return response;
  } catch (error) {
    console.error('Error in chatbot service:', error);

    // Provide a concise error response
    return "I apologize, but I'm having trouble processing that request. How else can I help you?";
  }
};

/**
 * Generate a contextually aware response from the knowledge base
 * @param {string} message - The user's message
 * @param {Array} history - Previous conversation history
 * @returns {string|null} - The generated response or null if not found in knowledge base
 */
const generateKnowledgeBaseResponse = (message, history = []) => {
  const normalizedMessage = message.toLowerCase();

  // Check for greetings with contextual awareness
  if (containsAny(normalizedMessage, ['hello', 'hi', 'hey', 'greetings'])) {
    // If this is a follow-up greeting in the conversation, respond differently
    if (history.length > 2) {
      return "Hello again! How can I help?";
    }
    return "Hello! How can I assist you?";
  }

  // Check for service inquiries with enhanced descriptions
  if (containsAny(normalizedMessage, ['service', 'offer', 'provide', 'help with', 'do you do', 'can you do'])) {
    // Check if asking about specific industry applications
    if (containsAny(normalizedMessage, ['healthcare', 'medical', 'health'])) {
      return "**Healthcare Industry Solutions**\n\n" +
             "Hrishikesh has extensive experience in the healthcare sector, offering specialized services including:\n\n" +
             "• **AI-Powered Patient Care**: Custom AI solutions that optimize patient care workflows, predict readmission risks, and analyze medical data for improved treatment outcomes.\n\n" +
             "• **Healthcare Management Systems**: Full-stack applications for medical practices that streamline appointment scheduling, patient records, billing, and compliance reporting.\n\n" +
             "• **Patient Engagement Platforms**: User-friendly patient portals and mobile apps that enhance communication, medication adherence, and remote monitoring capabilities.\n\n" +
             "These solutions have helped healthcare providers reduce administrative overhead by up to 35%, improve patient satisfaction scores by 28%, and enhance clinical decision-making through data-driven insights.\n\n" +
             "Would you like to discuss a specific healthcare project or learn more about other industry solutions?";
    }

    if (containsAny(normalizedMessage, ['finance', 'banking', 'financial'])) {
      return "**Financial Industry Solutions**\n\n" +
             "Hrishikesh has worked extensively with financial institutions and fintech companies, providing specialized services including:\n\n" +
             "• **Secure Banking Applications**: Robust, compliant financial platforms with advanced security features, real-time transaction processing, and intuitive user interfaces.\n\n" +
             "• **AI-Powered Fraud Detection**: Machine learning systems that identify suspicious patterns and potential fraud with 99.7% accuracy, reducing financial losses by up to 45%.\n\n" +
             "• **Financial Analytics Dashboards**: Comprehensive data visualization tools that transform complex financial data into actionable insights for better decision-making.\n\n" +
             "• **Fintech Strategy Consulting**: Market positioning, competitive analysis, and growth strategies that have helped fintech startups secure funding and achieve market differentiation.\n\n" +
             "Would you like to discuss a specific financial project or learn more about other industry solutions?";
    }

    // Check if asking about training, mentoring, or education
    if (containsAny(normalizedMessage, ['training', 'mentor', 'teach', 'learn', 'education', 'course', 'workshop', 'bootcamp', 'class'])) {
      return "**Tech Training & Mentorship Services**\n\n" +
             "Hrishikesh offers comprehensive training and mentorship programs designed to empower individuals and teams with the skills needed to excel in today's digital landscape:\n\n" +
             "• **Technology Stack Training**: Hands-on instruction in modern development technologies including React, Vue.js, TypeScript, Node.js, Kotlin, Electron, and Astro, tailored to your current skill level and learning goals.\n\n" +
             "• **AI & Automation Education**: Practical training in AI concepts, prompt engineering, LLM integration, and building intelligent workflows that enhance productivity and innovation.\n\n" +
             "• **Design & Creative Tools Workshops**: Comprehensive instruction in industry-standard design and creative tools including Adobe Creative Suite, Figma, Sketch, Blender, and Cinema 4D.\n\n" +
             "• **Entrepreneurial Mentorship**: Strategic guidance for tech entrepreneurs and startups, covering product development, market positioning, scaling strategies, and technical implementation.\n\n" +
             "Training formats include 1-on-1 mentorship sessions, interactive workshops, corporate training programs, and customized online classes. All programs emphasize practical, project-based learning with real-world applications.\n\n" +
             "Would you like to discuss your specific learning goals or explore a particular training program?";
    }

    // AI Development service detailed description
    if (containsAny(normalizedMessage, ['ai', 'artificial intelligence', 'machine learning', 'ml'])) {
      return "**AI Development & Strategy Services**\n\n" +
             "Hrishikesh offers comprehensive AI solutions that transform businesses through cutting-edge technology:\n\n" +
             "• **Custom AI Model Development**: Building tailored machine learning models that address specific business challenges, from predictive analytics to natural language processing.\n\n" +
             "• **AI Integration**: Seamlessly incorporating AI capabilities into existing systems and workflows to enhance functionality without disrupting operations.\n\n" +
             "• **Computer Vision Solutions**: Developing visual recognition systems for quality control, security monitoring, and automated inspection processes.\n\n" +
             "• **NLP Implementation**: Creating language processing systems for customer service automation, sentiment analysis, and content generation.\n\n" +
             "• **AI Strategy Consulting**: Developing roadmaps for AI adoption that align with business objectives, including feasibility studies, ROI analysis, and implementation planning.\n\n" +
             "Hrishikesh takes a business-first approach to AI, ensuring solutions address real problems rather than implementing technology for its own sake. His AI projects have delivered measurable ROI, including 40% improvements in operational efficiency and 25-35% cost reductions in applicable processes.\n\n" +
             "Would you like to discuss how AI solutions could address your specific business needs?";
    }

    // Full-Stack Development service detailed description
    if (containsAny(normalizedMessage, ['full-stack', 'web', 'development', 'application', 'software'])) {
      return "**Full-Stack Development Services**\n\n" +
             "Hrishikesh provides end-to-end web and mobile application development with a focus on scalability, performance, and user experience:\n\n" +
             "• **Web Application Development**: Creating responsive, feature-rich web applications using modern frameworks (React, Vue, Angular) with robust backend systems (Node.js, Python, PHP).\n\n" +
             "• **Mobile App Development**: Building native and cross-platform mobile applications for iOS and Android that deliver exceptional user experiences.\n\n" +
             "• **API Development & Integration**: Designing and implementing RESTful and GraphQL APIs that enable seamless communication between systems and third-party services.\n\n" +
             "• **Database Design & Implementation**: Creating optimized database architectures (SQL and NoSQL) that ensure data integrity, security, and performance.\n\n" +
             "• **Cloud Infrastructure Setup**: Deploying and managing applications on AWS, Azure, or Google Cloud with proper scaling, monitoring, and security configurations.\n\n" +
             "• **DevOps & CI/CD Integration**: Implementing automated testing, continuous integration, and deployment pipelines that streamline development workflows.\n\n" +
             "Hrishikesh emphasizes clean code architecture, performance optimization, and future-proof design patterns. His development projects have helped businesses achieve 30-45% improvements in application performance, 50-70% faster development cycles, and significant enhancements in user satisfaction metrics.\n\n" +
             "Would you like to discuss a specific development project or learn more about his technical capabilities?";
    }

    // Business Strategy service detailed description
    if (containsAny(normalizedMessage, ['business', 'strategy', 'consulting', 'transformation'])) {
      return "**Business Strategy Services**\n\n" +
             "Hrishikesh provides strategic business consulting to help organizations navigate challenges and capitalize on opportunities:\n\n" +
             "• **Market Analysis**: Comprehensive research on market trends, customer needs, and competitive landscapes to identify opportunities and threats.\n\n" +
             "• **Digital Transformation**: Strategic roadmaps for organizations transitioning to digital-first operations, including technology selection, process redesign, and change management.\n\n" +
             "• **Growth Strategy**: Data-driven approaches to identify and pursue new revenue streams, market segments, and business models.\n\n" +
             "• **Product Strategy**: End-to-end product development frameworks, from concept validation to market entry and scaling.\n\n" +
             "• **Innovation Consulting**: Structured methodologies to foster innovation within organizations, including ideation workshops, prototype development, and implementation planning.\n\n" +
             "With experience founding and growing Ajinkya Creatiion, Hrishikesh brings practical insights to business strategy, particularly for technology-focused companies. His strategic consulting has helped clients achieve 35% revenue growth, 25-40% operational efficiency improvements, and successful digital transformations that positioned them as industry leaders.\n\n" +
             "Would you like to discuss how strategic consulting could help your business navigate its current challenges?";
    }

    // Design service detailed description
    if (containsAny(normalizedMessage, ['design', 'ui', 'ux', 'user experience', 'graphic'])) {
      return "**Design Services**\n\n" +
             "Hrishikesh offers comprehensive design solutions that enhance user experience and strengthen brand identity:\n\n" +
             "• **UI/UX Design**: Creating intuitive, engaging user interfaces and experiences for web and mobile applications that drive user satisfaction and conversion rates.\n\n" +
             "• **Brand Identity Design**: Developing cohesive visual identities including logos, typography, color palettes, and usage guidelines that establish strong market recognition.\n\n" +
             "• **Website Design**: Crafting visually appealing, responsive websites that effectively communicate brand values and guide users toward desired actions.\n\n" +
             "• **Graphic Design**: Producing marketing materials, social media assets, and digital content that reinforce brand messaging and engage target audiences.\n\n" +
             "• **Design Systems**: Building comprehensive design frameworks that ensure consistency across products and streamline the development process.\n\n" +
             "Hrishikesh began his career in graphic design and maintains a strong aesthetic sensibility across all his work. His design projects have resulted in 30-45% increases in conversion rates, 40-60% improvements in user engagement metrics, and significant enhancements in brand perception and recognition.\n\n" +
             "Would you like to discuss a specific design project or see examples of his design work?";
    }

    // General services overview with enhanced descriptions
    return "**Hrishikesh's Professional Services**\n\n" +
           "Hrishikesh offers a comprehensive range of services that can be tailored to your specific business needs:\n\n" +
           "1. **AI Development & Strategy**: Custom AI solutions and strategic consulting to transform your business with cutting-edge technology, including machine learning models, natural language processing, and computer vision applications.\n\n" +
           "2. **Full-Stack Development**: End-to-end web and mobile application development with modern technologies and best practices, focusing on scalability, performance, and exceptional user experience.\n\n" +
           "3. **Business Strategy**: Strategic consulting to help you navigate challenges and capitalize on opportunities, including market analysis, digital transformation, growth strategies, and innovation frameworks.\n\n" +
           "4. **Design Services**: User-centered design solutions that enhance user experience and drive engagement, including UI/UX design, brand identity development, and comprehensive design systems.\n\n" +
           "These services can be provided individually or as part of an integrated solution. Hrishikesh's interdisciplinary approach ensures that technical solutions are aligned with business objectives and deliver measurable results.\n\n" +
           "Which service area would you like to explore in more detail?";
  }

  // Check for specific service inquiries with detailed context
  for (const service of CHATBOT_KNOWLEDGE.services) {
    if (normalizedMessage.includes(service.name.toLowerCase())) {
      // Provide a more thoughtful, context-rich response
      const detailsList = service.details.map(d => `• ${d}`).join('\n');

      return `**${service.name}**\n\n${service.description}\n\nServices include:\n${detailsList}\n\n**Expertise**: ${service.context.expertise}\n\n**Approach**: ${service.context.approach}\n\n**Ideal for**: ${service.context.ideal_for}\n\n**Case Study**: ${service.context.case_study}\n\nWould you like to discuss how ${service.name} could address your specific needs?`;
    }
  }

  // Check for about inquiries with more personal context
  if (containsAny(normalizedMessage, ['about', 'who', 'background', 'experience', 'bio'])) {
    // Check for specific aspects of background
    if (containsAny(normalizedMessage, ['education', 'learn', 'study', 'training'])) {
      return `**Hrishikesh's Education and Learning Journey**\n\n${CHATBOT_KNOWLEDGE.about.education}\n\nHis interdisciplinary knowledge allows him to approach problems from multiple angles and create integrated solutions. Is there a specific aspect of his background you're interested in?`;
    }

    if (containsAny(normalizedMessage, ['philosophy', 'approach', 'methodology', 'values'])) {
      return `**Hrishikesh's Philosophy**\n\n${CHATBOT_KNOWLEDGE.about.philosophy}\n\nThis philosophy guides all of his work, from AI development to business strategy. Would you like to know how this approach might apply to your project?`;
    }

    // Comprehensive about response with career highlights
    const highlights = CHATBOT_KNOWLEDGE.about.career_highlights.map(h => `• ${h}`).join('\n');
    return `**About Hrishikesh Mohite**\n\n${CHATBOT_KNOWLEDGE.about.background}\n\nWith ${CHATBOT_KNOWLEDGE.about.experience}, Hrishikesh has completed ${CHATBOT_KNOWLEDGE.about.projects} for ${CHATBOT_KNOWLEDGE.about.clients}.\n\n**Career Highlights**:\n${highlights}\n\nHis interdisciplinary background allows him to bridge technical possibilities with practical business applications. What specific aspect of his background would you like to explore further?`;
  }

  // Check for contact inquiries with lead generation focus
  if (containsAny(normalizedMessage, ['contact', 'email', 'reach', 'touch', 'connect', 'get in touch', 'hire', 'work with'])) {
    // If asking about a specific project or service
    if (containsAny(normalizedMessage, ['project', 'service', 'work', 'help', 'need'])) {
      return `**Let's Discuss Your Project**\n\n` +
             `Hrishikesh would be happy to discuss how he can help with your specific needs. To get started, please provide some brief details about your project:\n\n` +
             `1. What type of service are you interested in? (AI Development, Full-Stack Development, Business Strategy, or Design)\n` +
             `2. What are your main goals or challenges?\n` +
             `3. What is your preferred timeline?\n\n` +
             `You can share this information via:\n\n` +
             `• Email: [${CHATBOT_KNOWLEDGE.contact.email}](mailto:${CHATBOT_KNOWLEDGE.contact.email})\n` +
             `• Contact Form: [Submit Project Details](/contact)\n\n` +
             `Hrishikesh typically responds within 24-48 hours with initial thoughts and next steps for collaboration.`;
    }

    // If asking about consultation or advice
    if (containsAny(normalizedMessage, ['consult', 'advice', 'guidance', 'opinion', 'expertise'])) {
      return `**Book a Consultation with Hrishikesh**\n\n` +
             `Hrishikesh offers consultation sessions to discuss your business challenges, technology needs, or project ideas. These sessions provide valuable insights and actionable recommendations.\n\n` +
             `To schedule a consultation:\n\n` +
             `• Email: [${CHATBOT_KNOWLEDGE.contact.email}](mailto:${CHATBOT_KNOWLEDGE.contact.email}) with the subject "Consultation Request"\n` +
             `• Include your availability, time zone, and a brief overview of what you'd like to discuss\n` +
             `• Specify whether you prefer a video call or phone call\n\n` +
             `Consultations are typically scheduled within a week of your request, depending on availability. Would you like me to help you draft an initial inquiry email?`;
    }

    // General contact information with lead generation focus
    return `**Get in Touch with Hrishikesh**\n\n` +
           `Ready to discuss your project, explore collaboration opportunities, or learn more about Hrishikesh's services? Here's how to reach him:\n\n` +
           `• Email: [${CHATBOT_KNOWLEDGE.contact.email}](mailto:${CHATBOT_KNOWLEDGE.contact.email})\n` +
           `• LinkedIn: [Connect on LinkedIn](${CHATBOT_KNOWLEDGE.contact.linkedin})\n` +
           `• Instagram: [Follow on Instagram](${CHATBOT_KNOWLEDGE.contact.instagram})\n` +
           `• YouTube: [Subscribe on YouTube](${CHATBOT_KNOWLEDGE.contact.youtube})\n` +
           `• Website: [Ajinkya Creatiion](${CHATBOT_KNOWLEDGE.contact.website})\n` +
           `• Contact Form: [Submit an Inquiry](/contact)\n\n` +
           `${CHATBOT_KNOWLEDGE.contact.response_time}\n\n` +
           `When reaching out, it helps to include:\n` +
           `• A brief description of your project or needs\n` +
           `• Your timeline and budget considerations\n` +
           `• Any specific questions you have\n\n` +
           `This information allows Hrishikesh to provide a more tailored response to your inquiry. Would you like to discuss a specific project or service?`;
  }

  // Check for project inquiries with more specific information
  if (containsAny(normalizedMessage, ['project', 'portfolio', 'work', 'case study', 'example'])) {
    // Portfolio walkthrough - AI projects
    if (containsAny(normalizedMessage, ['ai', 'artificial intelligence', 'machine learning', 'nlp', 'computer vision'])) {
      return "**AI Projects in Hrishikesh's Portfolio**\n\n" +
             "1. **Intelligent Customer Service Platform**: A natural language processing system that analyzes customer inquiries, categorizes them, and provides automated responses with 85% accuracy. This reduced response times by 40% and improved customer satisfaction scores by 25%.\n\n" +
             "2. **Predictive Analytics Dashboard**: A business intelligence tool that uses machine learning to forecast sales trends, inventory needs, and customer behavior. This helped clients reduce inventory costs by 18% while maintaining optimal stock levels.\n\n" +
             "3. **Computer Vision Quality Control System**: An automated visual inspection system for manufacturing that identifies defects with 99.2% accuracy, reducing manual inspection time by 75% and improving overall product quality.\n\n" +
             "Would you like more details about any of these AI projects or would you prefer to see projects in another category? You can also explore all AI projects [here](/portfolio?category=ai).";
    }

    // Portfolio walkthrough - Web Development projects
    if (containsAny(normalizedMessage, ['web', 'website', 'application', 'app', 'development', 'full-stack'])) {
      return "**Web Development Projects in Hrishikesh's Portfolio**\n\n" +
             "1. **E-commerce Platform**: A fully responsive online store with integrated payment processing, inventory management, and customer analytics. Built with React, Node.js, and MongoDB, it increased the client's online sales by 45% in the first quarter.\n\n" +
             "2. **Real-time Collaboration Tool**: A web application that allows teams to collaborate on documents, designs, and project management in real-time. Built with Vue.js and Firebase, it improved team productivity by 30% for distributed teams.\n\n" +
             "3. **Enterprise Resource Planning System**: A comprehensive ERP solution customized for a manufacturing company, integrating operations, finance, and supply chain management. This streamlined workflows and reduced administrative overhead by 40%.\n\n" +
             "Would you like to know more about any of these web projects or would you prefer to see projects in another category? You can also explore all web development projects [here](/portfolio?category=web).";
    }

    // Portfolio walkthrough - Business Strategy projects
    if (containsAny(normalizedMessage, ['business', 'strategy', 'consulting', 'transformation', 'digital'])) {
      return "**Business Strategy Projects in Hrishikesh's Portfolio**\n\n" +
             "1. **Digital Transformation Roadmap**: Developed a comprehensive digital transformation strategy for a traditional retail business, including e-commerce integration, customer data analytics, and staff training. This resulted in a 35% revenue increase within the first year.\n\n" +
             "2. **Market Entry Strategy**: Created a data-driven market entry plan for a SaaS startup, identifying target segments, competitive positioning, and growth metrics. This helped secure Series A funding and achieve 200% year-over-year growth.\n\n" +
             "3. **Operational Efficiency Analysis**: Conducted a thorough analysis of business processes for a mid-sized manufacturer, identifying bottlenecks and implementing lean methodologies. This improved production efficiency by 28% and reduced operational costs by 15%.\n\n" +
             "Would you like more information about any of these business strategy projects or would you prefer to see projects in another category? You can also explore all business strategy projects [here](/portfolio?category=strategy).";
    }

    // Portfolio walkthrough - Design projects
    if (containsAny(normalizedMessage, ['design', 'ui', 'ux', 'user interface', 'user experience', 'graphic'])) {
      return "**Design Projects in Hrishikesh's Portfolio**\n\n" +
             "1. **E-commerce UX Redesign**: Completely redesigned the user experience for an online retailer, focusing on simplified navigation, streamlined checkout, and mobile optimization. This resulted in a 45% increase in conversion rate and 30% reduction in cart abandonment.\n\n" +
             "2. **Brand Identity System**: Developed a comprehensive brand identity for a tech startup, including logo design, typography, color palette, and usage guidelines. This helped the company establish immediate market recognition and secure investor funding.\n\n" +
             "3. **Mobile App UI Design**: Created an intuitive, visually appealing interface for a health and wellness app, incorporating user feedback through multiple iterations. The final design achieved a 4.8/5 user satisfaction rating and increased user engagement by 60%.\n\n" +
             "Would you like more details about any of these design projects or would you prefer to see projects in another category? You can also explore all design projects [here](/portfolio?category=design).";
    }

    // General portfolio walkthrough
    return "**Hrishikesh's Portfolio Highlights**\n\n" +
           "Hrishikesh has completed over 100 projects across various domains, demonstrating his versatility and problem-solving abilities. His portfolio is organized into these main categories:\n\n" +
           "1. **AI Development**: Projects including natural language processing systems, predictive analytics tools, and computer vision applications.\n\n" +
           "2. **Web & Mobile Applications**: Full-stack development projects ranging from e-commerce platforms to enterprise systems and mobile apps.\n\n" +
           "3. **Business Strategy**: Digital transformation roadmaps, market entry strategies, and operational efficiency analyses.\n\n" +
           "4. **Design Work**: UX/UI redesigns, brand identity systems, and mobile app interfaces.\n\n" +
           "Each project showcases his ability to deliver practical solutions that address real business needs and generate measurable results. Which category would you like to explore further? You can also browse the complete portfolio [here](/portfolio).";
  }

  // Check for book inquiries with more context
  if (containsAny(normalizedMessage, ['book', 'author', 'writing', 'read', 'publication'])) {
    const topics = CHATBOT_KNOWLEDGE.books.topics.join(', ');
    return `As an author, Hrishikesh writes about ${topics}. His books are written for ${CHATBOT_KNOWLEDGE.books.audience}.\n\n${CHATBOT_KNOWLEDGE.books.philosophy}\n\nYou can explore his published works and upcoming titles in the [Books section](/books) of this website. Would you like recommendations based on your specific interests?`;
  }

  // Check for FAQ questions
  for (const faq of CHATBOT_KNOWLEDGE.faq) {
    // Check if the message contains key phrases from the question
    const questionWords = faq.question.toLowerCase().split(' ').filter(word => word.length > 4);
    if (questionWords.some(word => normalizedMessage.includes(word))) {
      return `**${faq.question}**\n\n${faq.answer}`;
    }
  }

  // Check for thank you with contextual awareness
  if (containsAny(normalizedMessage, ['thank', 'thanks', 'appreciate', 'helpful'])) {
    if (history.length > 4) {
      return "You're welcome! Anything else I can help with?";
    }
    return "You're welcome! Let me know if you need anything else.";
  }

  // Handle numeric responses (user selected an option)
  if (/^[1-5]$/.test(normalizedMessage.trim())) {
    const optionMap = {
      '1': 'services',
      '2': 'about',
      '3': 'portfolio',
      '4': 'contact',
      '5': 'books'
    };

    const selectedOption = optionMap[normalizedMessage.trim()];

    if (selectedOption === 'services') {
      const servicesList = CHATBOT_KNOWLEDGE.services.map(s => `• **${s.name}**: ${s.description}`).join('\n\n');
      return `Here are the services Hrishikesh offers:\n\n${servicesList}\n\nWhich service would you like to know more about?`;
    }

    if (selectedOption === 'about') {
      const highlights = CHATBOT_KNOWLEDGE.about.career_highlights.map(h => `• ${h}`).join('\n');
      return `**About Hrishikesh Mohite**\n\n${CHATBOT_KNOWLEDGE.about.background}\n\nWith ${CHATBOT_KNOWLEDGE.about.experience}, Hrishikesh has completed ${CHATBOT_KNOWLEDGE.about.projects} for ${CHATBOT_KNOWLEDGE.about.clients}.\n\n**Career Highlights**:\n${highlights}\n\nWhat specific aspect of his background would you like to explore further?`;
    }

    if (selectedOption === 'portfolio') {
      return "Hrishikesh has worked on a diverse range of projects that demonstrate his versatility and problem-solving abilities. His portfolio includes AI implementations, web and mobile applications, business transformation strategies, and creative design work. Each project showcases his ability to deliver practical solutions that address real business needs. You can explore his portfolio [here](/portfolio) to see examples organized by category.";
    }

    if (selectedOption === 'contact') {
      return `You can contact Hrishikesh through:\n\n• Email: [${CHATBOT_KNOWLEDGE.contact.email}](mailto:${CHATBOT_KNOWLEDGE.contact.email})\n• LinkedIn: [LinkedIn Profile](${CHATBOT_KNOWLEDGE.contact.linkedin})\n• Instagram: [Instagram Profile](${CHATBOT_KNOWLEDGE.contact.instagram})\n• YouTube: [YouTube Channel](${CHATBOT_KNOWLEDGE.contact.youtube})\n• Website: [Ajinkya Creatiion](${CHATBOT_KNOWLEDGE.contact.website})\n\n${CHATBOT_KNOWLEDGE.contact.response_time}. ${CHATBOT_KNOWLEDGE.contact.preferred_contact}\n\nYou can also use the [contact form](/contact) on this website for a structured inquiry.`;
    }

    if (selectedOption === 'books') {
      const topics = CHATBOT_KNOWLEDGE.books.topics.join(', ');
      return `As an author, Hrishikesh writes about ${topics}. His books are written for ${CHATBOT_KNOWLEDGE.books.audience}.\n\n${CHATBOT_KNOWLEDGE.books.philosophy}\n\nYou can explore his published works and upcoming titles in the [Books section](/books) of this website.`;
    }
  }

  // Handle text-based option selections
  if (normalizedMessage === 'services' || normalizedMessage === 'service') {
    const servicesList = CHATBOT_KNOWLEDGE.services.map(s => `• **${s.name}**: ${s.description}`).join('\n\n');
    return `Here are the services Hrishikesh offers:\n\n${servicesList}\n\nWhich service would you like to know more about?`;
  }

  if (normalizedMessage === 'about') {
    const highlights = CHATBOT_KNOWLEDGE.about.career_highlights.map(h => `• ${h}`).join('\n');
    return `**About Hrishikesh Mohite**\n\n${CHATBOT_KNOWLEDGE.about.background}\n\nWith ${CHATBOT_KNOWLEDGE.about.experience}, Hrishikesh has completed ${CHATBOT_KNOWLEDGE.about.projects} for ${CHATBOT_KNOWLEDGE.about.clients}.\n\n**Career Highlights**:\n${highlights}\n\nWhat specific aspect of his background would you like to explore further?`;
  }

  if (normalizedMessage === 'portfolio') {
    return "Hrishikesh has worked on a diverse range of projects that demonstrate his versatility and problem-solving abilities. His portfolio includes AI implementations, web and mobile applications, business transformation strategies, and creative design work. Each project showcases his ability to deliver practical solutions that address real business needs. You can explore his portfolio [here](/portfolio) to see examples organized by category.";
  }

  if (normalizedMessage === 'contact') {
    return `You can contact Hrishikesh through:\n\n• Email: [${CHATBOT_KNOWLEDGE.contact.email}](mailto:${CHATBOT_KNOWLEDGE.contact.email})\n• LinkedIn: [LinkedIn Profile](${CHATBOT_KNOWLEDGE.contact.linkedin})\n• Instagram: [Instagram Profile](${CHATBOT_KNOWLEDGE.contact.instagram})\n• YouTube: [YouTube Channel](${CHATBOT_KNOWLEDGE.contact.youtube})\n• Website: [Ajinkya Creatiion](${CHATBOT_KNOWLEDGE.contact.website})\n\n${CHATBOT_KNOWLEDGE.contact.response_time}. ${CHATBOT_KNOWLEDGE.contact.preferred_contact}\n\nYou can also use the [contact form](/contact) on this website for a structured inquiry.`;
  }

  if (normalizedMessage === 'books' || normalizedMessage === 'book') {
    const topics = CHATBOT_KNOWLEDGE.books.topics.join(', ');
    return `As an author, Hrishikesh writes about ${topics}. His books are written for ${CHATBOT_KNOWLEDGE.books.audience}.\n\n${CHATBOT_KNOWLEDGE.books.philosophy}\n\nYou can explore his published works and upcoming titles in the [Books section](/books) of this website.`;
  }

  // Return null if no match found in knowledge base
  return null;
};

/**
 * Generate a contextually aware response based on website information
 * @param {string} message - The user's message
 * @param {Array} websiteInfo - Relevant website sections
 * @param {Object|null} expertiseInfo - Information about a specific expertise area
 * @param {Array} history - Previous conversation history
 * @returns {Promise<string>} - The generated response with website information
 */
const generateWebsiteResponse = async (message, websiteInfo, expertiseInfo, history) => {
  const normalizedMessage = message.toLowerCase();

  // Analyze conversation context from history
  const isFollowUpQuestion = history.length >= 2;
  const previousTopics = isFollowUpQuestion ?
    extractPreviousTopics(history.slice(-2)) : [];

  // If we have information about a specific expertise area
  if (expertiseInfo) {
    const detailsList = expertiseInfo.details.map(d => `• ${d}`).join('\n');

    // If this is a follow-up question about the same expertise area
    if (isFollowUpQuestion && previousTopics.includes(expertiseInfo.title.toLowerCase())) {
      return `To add more detail about **${expertiseInfo.title}**, Hrishikesh's approach is ${expertiseInfo.context.approach}\n\nThis service is ideal for ${expertiseInfo.context.ideal_for}\n\nIn a recent project, ${expertiseInfo.context.case_study}\n\nWould you like to discuss how this expertise could be applied to your specific needs?`;
    }

    // If asking about practical applications
    if (containsAny(normalizedMessage, ['example', 'case', 'practical', 'real world', 'application'])) {
      return `**${expertiseInfo.title} - Practical Applications**\n\n${expertiseInfo.description}\n\n${expertiseInfo.context.case_study}\n\nThis approach has proven effective across various industries, with solutions tailored to each client's specific challenges and objectives. Would you like to discuss how similar approaches might work for your situation?`;
    }

    // Standard expertise response with enhanced context
    return `**${expertiseInfo.title}**\n\n${expertiseInfo.description}\n\nServices include:\n${detailsList}\n\n${expertiseInfo.context.expertise}\n\nWould you like to know more about specific applications or how this expertise could address your needs?`;
  }

  // If we have relevant website sections
  if (websiteInfo && websiteInfo.length > 0) {
    // Prioritize the most relevant section based on the query
    const prioritizedSections = prioritizeSections(websiteInfo, normalizedMessage);

    // Format website sections into a readable response with navigation help
    const formattedSections = prioritizedSections.map(section => {
      return `**${section.title}**\n${section.description}\n\n**Navigation Tip**: ${section.navigation_help}\n\n[Visit ${section.title} page](${section.path})`;
    }).join('\n\n');

    // For specific information requests
    if (containsAny(normalizedMessage, ['what is', 'how to', 'explain', 'tell me about', 'information'])) {
      // If this is a follow-up question, provide more specific information
      if (isFollowUpQuestion) {
        const mostRelevantSection = prioritizedSections[0];
        return `To elaborate on the ${mostRelevantSection.title} section: This area of the website provides detailed information about ${mostRelevantSection.description.toLowerCase()} You'll find comprehensive details, examples, and contact options there.\n\nIs there something specific about ${mostRelevantSection.title.toLowerCase()} you'd like me to explain?`;
      }

      return `Here's the information you're looking for:\n\n${formattedSections}\n\nEach section of the website is designed to provide comprehensive information about that aspect of Hrishikesh's work. Which area would you like to explore in more detail?`;
    }

    // For navigation queries with practical guidance
    if (containsAny(normalizedMessage, ['find', 'where', 'navigate', 'go to', 'show'])) {
      return `You can find what you're looking for in these sections of the website:\n\n${formattedSections}\n\nThe navigation menu at the top of the page also provides quick access to all main sections. Would you like me to explain what you'll find in any of these areas?`;
    }

    // For comparison queries
    if (containsAny(normalizedMessage, ['compare', 'difference', 'versus', 'vs', 'better'])) {
      return `I notice you're interested in comparing options. Hrishikesh's website is organized to help you understand the different aspects of his work:\n\n${formattedSections}\n\nEach section provides detailed information that can help you make informed decisions. Would you like me to highlight the key differences between any specific areas?`;
    }

    // For general queries with contextual awareness
    return `Based on your question, here are the most relevant sections of the website:\n\n${formattedSections}\n\nThese sections contain detailed information that should address your query. Is there a specific aspect you'd like me to elaborate on?`;
  }

  // If no relevant information was found, provide guided options
  // Check if the query is about something outside the scope of the website
  const outOfScopeKeywords = [
    'price', 'cost', 'fee', 'payment', 'charge', 'expensive', 'affordable',
    'location', 'address', 'office', 'visit', 'meet', 'appointment',
    'job', 'career', 'employment', 'hire', 'position', 'vacancy', 'opening', 'internship',
    'weather', 'news', 'sports', 'politics', 'movie', 'music', 'game', 'travel', 'hotel',
    'restaurant', 'food', 'recipe', 'diet', 'exercise', 'health', 'medicine', 'doctor',
    'stock', 'invest', 'crypto', 'bitcoin', 'ethereum', 'nft', 'blockchain'
  ];

  // If the query contains out-of-scope keywords, provide guided options
  if (outOfScopeKeywords.some(keyword => normalizedMessage.includes(keyword))) {
    return generateGuidedOptions(message);
  }

  // Handle numeric responses (user selected an option)
  if (/^[1-5]$/.test(normalizedMessage.trim())) {
    const optionMap = {
      '1': 'services',
      '2': 'about',
      '3': 'portfolio',
      '4': 'contact',
      '5': 'books'
    };

    const selectedOption = optionMap[normalizedMessage.trim()];

    if (selectedOption === 'services') {
      const servicesList = CHATBOT_KNOWLEDGE.services.map(s => `• **${s.name}**: ${s.description}`).join('\n\n');
      return `Here are the services Hrishikesh offers:\n\n${servicesList}\n\nWhich service would you like to know more about?`;
    }

    if (selectedOption === 'about') {
      const highlights = CHATBOT_KNOWLEDGE.about.career_highlights.map(h => `• ${h}`).join('\n');
      return `**About Hrishikesh Mohite**\n\n${CHATBOT_KNOWLEDGE.about.background}\n\nWith ${CHATBOT_KNOWLEDGE.about.experience}, Hrishikesh has completed ${CHATBOT_KNOWLEDGE.about.projects} for ${CHATBOT_KNOWLEDGE.about.clients}.\n\n**Career Highlights**:\n${highlights}\n\nWhat specific aspect of his background would you like to explore further?`;
    }

    if (selectedOption === 'portfolio') {
      return "Hrishikesh has worked on a diverse range of projects that demonstrate his versatility and problem-solving abilities. His portfolio includes AI implementations, web and mobile applications, business transformation strategies, and creative design work. Each project showcases his ability to deliver practical solutions that address real business needs. You can explore his portfolio [here](/portfolio) to see examples organized by category.";
    }

    if (selectedOption === 'contact') {
      return `You can contact Hrishikesh through:\n\n• Email: [${CHATBOT_KNOWLEDGE.contact.email}](mailto:${CHATBOT_KNOWLEDGE.contact.email})\n• LinkedIn: [LinkedIn Profile](${CHATBOT_KNOWLEDGE.contact.linkedin})\n• Website: [Ajinkya Creatiion](${CHATBOT_KNOWLEDGE.contact.website})\n\n${CHATBOT_KNOWLEDGE.contact.response_time}. ${CHATBOT_KNOWLEDGE.contact.preferred_contact}\n\nYou can also use the [contact form](/contact) on this website for a structured inquiry.`;
    }

    if (selectedOption === 'books') {
      const topics = CHATBOT_KNOWLEDGE.books.topics.join(', ');
      return `As an author, Hrishikesh writes about ${topics}. His books are written for ${CHATBOT_KNOWLEDGE.books.audience}.\n\n${CHATBOT_KNOWLEDGE.books.philosophy}\n\nYou can explore his published works and upcoming titles in the [Books section](/books) of this website.`;
    }
  }

  // If it's a follow-up question but we don't have relevant information
  if (isFollowUpQuestion) {
    return generateGuidedOptions(message);
  }

  // Default response with guided options
  return generateGuidedOptions(message);
};

/**
 * Extract topics from previous conversation messages
 * @param {Array} messages - Previous messages in the conversation
 * @returns {Array} - Array of topic keywords
 */
const extractPreviousTopics = (messages) => {
  const topicKeywords = [];

  // Extract service names
  const serviceNames = CHATBOT_KNOWLEDGE.services.map(s =>
    s.name.toLowerCase()
  );

  // Extract section names
  const sectionNames = ['about', 'services', 'portfolio', 'books', 'contact'];

  // Check each message for topics
  messages.forEach(msg => {
    if (msg.type === 'bot') {
      const text = msg.text.toLowerCase();

      // Check for service names
      serviceNames.forEach(name => {
        if (text.includes(name)) {
          topicKeywords.push(name);
        }
      });

      // Check for section names
      sectionNames.forEach(name => {
        if (text.includes(name)) {
          topicKeywords.push(name);
        }
      });

      // Check for other common topics
      ['ai', 'development', 'design', 'strategy', 'business', 'project'].forEach(topic => {
        if (text.includes(topic)) {
          topicKeywords.push(topic);
        }
      });
    }
  });

  // Return unique topics
  return [...new Set(topicKeywords)];
};

/**
 * Prioritize website sections based on query relevance
 * @param {Array} sections - Website sections
 * @param {string} query - The user's query
 * @returns {Array} - Prioritized sections
 */
const prioritizeSections = (sections, query) => {
  // Create a copy to avoid modifying the original
  const prioritized = [...sections];

  // Score each section based on relevance to the query
  const scoredSections = prioritized.map(section => {
    let score = 0;

    // Check title match
    if (section.title.toLowerCase().includes(query)) {
      score += 10;
    }

    // Check description match
    if (section.description.toLowerCase().includes(query)) {
      score += 5;
    }

    // Check for specific keywords
    const keywords = {
      'about': ['who', 'background', 'experience', 'bio'],
      'services': ['service', 'offer', 'provide', 'help'],
      'portfolio': ['project', 'work', 'example', 'case study'],
      'books': ['book', 'author', 'writing', 'read'],
      'contact': ['contact', 'email', 'reach', 'touch']
    };

    // Add score for keyword matches
    if (keywords[section.title.toLowerCase()]) {
      keywords[section.title.toLowerCase()].forEach(keyword => {
        if (query.includes(keyword)) {
          score += 3;
        }
      });
    }

    return { ...section, score };
  });

  // Sort by score (highest first)
  return scoredSections.sort((a, b) => b.score - a.score);
};

/**
 * Generate guided options for users when their query is out of scope
 * @param {string} message - The user's message
 * @returns {string} - Response with guided options
 */
const generateGuidedOptions = (message) => {
  const normalizedMessage = message.toLowerCase();

  // Determine which category of options to show based on the query
  if (containsAny(normalizedMessage, ['price', 'cost', 'fee', 'payment', 'charge', 'expensive', 'affordable'])) {
    return "I don't have specific pricing information. Here are some options that might help you:\n\n" +
           "1️⃣ **Services** - Learn about the services Hrishikesh offers\n" +
           "2️⃣ **Contact** - Get in touch to discuss your project and receive a custom quote\n" +
           "3️⃣ **Portfolio** - See examples of previous work\n\n" +
           "Please type the number or name of the option you're interested in.";
  }

  if (containsAny(normalizedMessage, ['location', 'address', 'office', 'visit', 'meet', 'appointment'])) {
    return "I don't have information about physical office locations. Here are some options that might help you:\n\n" +
           "1️⃣ **Contact** - Get Hrishikesh's contact information\n" +
           "2️⃣ **About** - Learn more about Hrishikesh\n" +
           "3️⃣ **Services** - Explore the services offered remotely\n\n" +
           "Please type the number or name of the option you're interested in.";
  }

  if (containsAny(normalizedMessage, ['job', 'career', 'employment', 'hire', 'position', 'vacancy', 'opening', 'internship'])) {
    return "I don't have information about job openings or career opportunities. Would you like to know about services, background, or contact information?";
  }

  // Concise response for out-of-scope queries
  return "I can help with information about services, background, portfolio, contact, or books. What would you like to know?";
};

/**
 * Check if a string contains any of the given keywords
 * @param {string} str - The string to check
 * @param {Array<string>} keywords - Keywords to look for
 * @returns {boolean} - True if any keyword is found
 */
const containsAny = (str, keywords) => {
  return keywords.some(keyword => str.includes(keyword));
};

