/**
 * Website Information Service
 *
 * This service provides methods to access information about <PERSON><PERSON><PERSON><PERSON><PERSON>'s
 * portfolio website, services, and expertise.
 */

// Website sections with enhanced navigation assistance
const WEBSITE_SECTIONS = {
  home: {
    title: 'Home',
    path: '/',
    description: '<PERSON><PERSON><PERSON><PERSON><PERSON> is an entrepreneur, AI strategist, full-stack developer, digital designer, and tech mentor with expertise in AI development, web applications, tech training, and business strategy.',
    navigation_help: 'The Home page provides an overview of <PERSON><PERSON><PERSON><PERSON><PERSON>\'s expertise and featured projects. From here, you can navigate to any section of the website using the main menu at the top of the page.'
  },
  about: {
    title: 'About',
    path: '/about',
    description: 'Learn about <PERSON><PERSON><PERSON><PERSON><PERSON>\'s professional journey, skills, and expertise in technology and design.',
    navigation_help: 'The About page details <PERSON><PERSON><PERSON><PERSON><PERSON>\'s background, career milestones, and professional philosophy. You\'ll find information about his experience, skills, and the values that drive his work.'
  },
  services: {
    title: 'Services',
    path: '/services',
    description: 'Explore the range of services offered by <PERSON><PERSON><PERSON><PERSON><PERSON>, including AI Development, Full-Stack Development, Tech Training & Mentorship, Business Strategy, and Design.',
    navigation_help: 'The Services page outlines all professional services <PERSON><PERSON>hikes<PERSON> offers. Each service category includes detailed descriptions, use cases, and the benefits you can expect. You can click on any service card to learn more about that specific offering.'
  },
  portfolio: {
    title: 'Portfolio',
    path: '/portfolio',
    description: 'View Hrishikesh Mohite\'s portfolio showcasing projects in AI, web development, and design.',
    navigation_help: 'The Portfolio page showcases Hrishikesh\'s past projects organized by category. Each project includes a description, technologies used, challenges overcome, and results achieved. You can filter projects by category using the navigation options on the page.'
  },
  books: {
    title: 'Books',
    path: '/books',
    description: 'Discover books authored by Hrishikesh Mohite on technology, innovation, entrepreneurship, and leadership.',
    navigation_help: 'The Books page features publications by Hrishikesh, including descriptions, topics covered, and where to purchase them. Each book entry includes a summary and key takeaways.'
  },
  contact: {
    title: 'Contact',
    path: '/contact',
    description: 'Get in touch with Hrishikesh Mohite for business inquiries, collaborations, or questions.',
    navigation_help: 'The Contact page provides a form to send inquiries directly to Hrishikesh. You\'ll also find his email address and links to professional social media profiles. For business inquiries, the contact form is the most direct method to reach him.'
  }
};

// Expertise areas with detailed information
const EXPERTISE_AREAS = {
  ai: {
    title: 'AI Development & Strategy',
    description: 'Custom AI solutions and strategic consulting to transform your business with cutting-edge technology.',
    details: [
      'Custom AI Model Development',
      'Machine Learning Implementation',
      'Natural Language Processing',
      'Computer Vision Solutions',
      'AI Strategy Consulting',
      'AI Integration with Existing Systems'
    ]
  },
  webdev: {
    title: 'Full-Stack Development',
    description: 'End-to-end web and mobile application development with modern technologies and best practices.',
    details: [
      'Web Application Development',
      'Mobile App Development',
      'API Development',
      'Database Design & Implementation',
      'Cloud Infrastructure Setup',
      'DevOps & CI/CD Integration'
    ]
  },
  training: {
    title: 'Tech Trainer & Mentor',
    description: 'Empowering aspiring developers, designers, and entrepreneurs with the knowledge and skills needed to excel in the digital landscape.',
    details: [
      'Full-Stack Development Training',
      'AI & Automation Education',
      'Design & Creative Tools Workshops',
      'Entrepreneurial Mentorship',
      '1-on-1 Mentorship Sessions',
      'Corporate Training Programs'
    ]
  },
  design: {
    title: 'Web & UI/UX Design',
    description: 'User-centered design solutions that enhance user experience and drive engagement.',
    details: [
      'Website Design',
      'User Interface Design',
      'User Experience Design',
      'Wireframing & Prototyping',
      'Responsive Design',
      'Design Systems'
    ]
  },
  business: {
    title: 'Business Strategy',
    description: 'Strategic business consulting to help you navigate challenges and capitalize on opportunities.',
    details: [
      'Market Analysis',
      'Competitive Research',
      'Growth Strategy',
      'Digital Transformation',
      'Product Strategy',
      'Innovation Consulting'
    ]
  }
};

/**
 * Get information about website sections and content
 * @param {string} query - The search query
 * @returns {Promise<Array>} - Array of relevant website sections
 */
export const getWebsiteInfo = async (query) => {
  try {
    // Add a small delay to simulate processing
    await new Promise(resolve => setTimeout(resolve, 300));

    // Find relevant website sections based on the query
    return findRelevantSections(query);
  } catch (error) {
    console.error('Error retrieving website information:', error);
    return [];
  }
};

/**
 * Get detailed information about a specific expertise area
 * @param {string} area - The expertise area to get information about
 * @returns {Promise<Object|null>} - Detailed information about the expertise area
 */
export const getExpertiseInfo = async (area) => {
  try {
    // Add a small delay to simulate processing
    await new Promise(resolve => setTimeout(resolve, 200));

    // Find the expertise area
    return findExpertiseArea(area);
  } catch (error) {
    console.error('Error retrieving expertise information:', error);
    return null;
  }
};

/**
 * Get information about Hrishikesh's services
 * @returns {Promise<Array>} - Array of services
 */
export const getServices = async () => {
  try {
    // Add a small delay to simulate processing
    await new Promise(resolve => setTimeout(resolve, 200));

    // Return all expertise areas as services
    return Object.values(EXPERTISE_AREAS);
  } catch (error) {
    console.error('Error retrieving services information:', error);
    return [];
  }
};

/**
 * Find relevant website sections based on a query
 * @param {string} query - The search query
 * @returns {Array} - Array of relevant website sections
 */
const findRelevantSections = (query) => {
  const normalizedQuery = query.toLowerCase();
  const results = [];

  // Check each website section for relevance
  for (const [_key, section] of Object.entries(WEBSITE_SECTIONS)) {
    // Check if the query matches the section title or description
    if (section.title.toLowerCase().includes(normalizedQuery) ||
        section.description.toLowerCase().includes(normalizedQuery)) {
      results.push({
        title: section.title,
        path: section.path,
        description: section.description
      });
    }
  }

  // Check for specific keywords
  if (normalizedQuery.includes('contact') || normalizedQuery.includes('email') || normalizedQuery.includes('reach')) {
    if (!results.some(r => r.title === 'Contact')) {
      results.push(WEBSITE_SECTIONS.contact);
    }
  }

  if (normalizedQuery.includes('portfolio') || normalizedQuery.includes('project') || normalizedQuery.includes('work')) {
    if (!results.some(r => r.title === 'Portfolio')) {
      results.push(WEBSITE_SECTIONS.portfolio);
    }
  }

  if (normalizedQuery.includes('service') || normalizedQuery.includes('offer') || normalizedQuery.includes('provide')) {
    if (!results.some(r => r.title === 'Services')) {
      results.push(WEBSITE_SECTIONS.services);
    }
  }

  if (normalizedQuery.includes('about') || normalizedQuery.includes('bio') || normalizedQuery.includes('background')) {
    if (!results.some(r => r.title === 'About')) {
      results.push(WEBSITE_SECTIONS.about);
    }
  }

  if (normalizedQuery.includes('book') || normalizedQuery.includes('author') || normalizedQuery.includes('read')) {
    if (!results.some(r => r.title === 'Books')) {
      results.push(WEBSITE_SECTIONS.books);
    }
  }

  // If no specific sections were found, return the home section
  if (results.length === 0) {
    results.push(WEBSITE_SECTIONS.home);
  }

  return results;
};

/**
 * Find expertise area based on a query
 * @param {string} query - The expertise area to find
 * @returns {Object|null} - The expertise area or null if not found
 */
const findExpertiseArea = (query) => {
  const normalizedQuery = query.toLowerCase();

  // Check for direct matches with expertise area keys
  for (const [key, area] of Object.entries(EXPERTISE_AREAS)) {
    if (key === normalizedQuery) {
      return area;
    }
  }

  // Check for matches in titles or descriptions
  for (const area of Object.values(EXPERTISE_AREAS)) {
    if (area.title.toLowerCase().includes(normalizedQuery) ||
        area.description.toLowerCase().includes(normalizedQuery)) {
      return area;
    }

    // Check for matches in details
    for (const detail of area.details) {
      if (detail.toLowerCase().includes(normalizedQuery)) {
        return area;
      }
    }
  }

  // Handle specific keywords
  if (normalizedQuery.includes('ai') ||
      normalizedQuery.includes('artificial intelligence') ||
      normalizedQuery.includes('machine learning')) {
    return EXPERTISE_AREAS.ai;
  }

  if (normalizedQuery.includes('web') ||
      normalizedQuery.includes('development') ||
      normalizedQuery.includes('programming') ||
      normalizedQuery.includes('coding')) {
    return EXPERTISE_AREAS.webdev;
  }

  if (normalizedQuery.includes('training') ||
      normalizedQuery.includes('mentor') ||
      normalizedQuery.includes('teach') ||
      normalizedQuery.includes('learn') ||
      normalizedQuery.includes('education') ||
      normalizedQuery.includes('workshop')) {
    return EXPERTISE_AREAS.training;
  }

  if (normalizedQuery.includes('design') ||
      normalizedQuery.includes('ui') ||
      normalizedQuery.includes('ux')) {
    return EXPERTISE_AREAS.design;
  }

  if (normalizedQuery.includes('business') ||
      normalizedQuery.includes('strategy') ||
      normalizedQuery.includes('consulting')) {
    return EXPERTISE_AREAS.business;
  }

  // No match found
  return null;
};
