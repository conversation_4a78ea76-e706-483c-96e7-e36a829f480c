/**
 * Analytics Utility
 * 
 * This utility provides functions for handling Google Analytics in a privacy-friendly way
 * that's compatible with Chrome's third-party cookie phase-out.
 */

import { hasConsentForCategory, COOKIE_CATEGORIES } from './cookieManager';

// Google Analytics Measurement ID
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX'; // Replace with your actual GA4 Measurement ID

/**
 * Initialize Google Analytics
 * 
 * This function initializes Google Analytics with privacy-focused settings
 * and only if the user has consented to analytics cookies.
 */
export const initializeAnalytics = () => {
  // Only initialize if user has consented to analytics cookies
  if (!hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS)) {
    console.log('Analytics not initialized because user hasn\'t consented to analytics cookies');
    return;
  }

  // Load Google Analytics script
  const script = document.createElement('script');
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  script.async = true;
  document.head.appendChild(script);

  // Initialize Google Analytics
  window.dataLayer = window.dataLayer || [];
  function gtag() {
    window.dataLayer.push(arguments);
  }
  gtag('js', new Date());

  // Configure GA4 with privacy-focused settings
  gtag('config', GA_MEASUREMENT_ID, {
    cookie_domain: 'auto',
    cookie_flags: 'SameSite=None;Secure',
    cookie_expires: 60 * 60 * 24 * 365, // 1 year
    anonymize_ip: true, // Anonymize IP addresses
    allow_google_signals: false, // Disable Google signals
    allow_ad_personalization_signals: false, // Disable ad personalization
    restricted_data_processing: true, // Enable restricted data processing
    send_page_view: true
  });

  // Store reference to gtag function
  window.gtag = gtag;

  console.log('Google Analytics initialized with privacy-focused settings');
};

/**
 * Track page view
 * 
 * @param {string} path - Page path
 * @param {string} title - Page title
 */
export const trackPageView = (path, title) => {
  if (!hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS) || !window.gtag) {
    return;
  }

  window.gtag('event', 'page_view', {
    page_path: path,
    page_title: title,
    page_location: window.location.href
  });
};

/**
 * Track event
 * 
 * @param {string} category - Event category
 * @param {string} action - Event action
 * @param {string} label - Event label
 * @param {number} value - Event value
 */
export const trackEvent = (category, action, label, value) => {
  if (!hasConsentForCategory(COOKIE_CATEGORIES.ANALYTICS) || !window.gtag) {
    return;
  }

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value
  });
};

/**
 * Update analytics consent
 * 
 * This function should be called when the user updates their analytics consent.
 * If the user has consented to analytics cookies, it initializes Google Analytics.
 * If the user has revoked consent, it disables Google Analytics.
 * 
 * @param {boolean} hasConsent - Whether the user has consented to analytics cookies
 */
export const updateAnalyticsConsent = (hasConsent) => {
  if (hasConsent) {
    // Initialize Google Analytics if not already initialized
    if (!window.gtag) {
      initializeAnalytics();
    }
  } else {
    // Disable Google Analytics
    if (window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: 'denied'
      });
      
      // Remove Google Analytics cookies
      const cookies = document.cookie.split(';');
      for (let i = 0; i < cookies.length; i++) {
        const cookie = cookies[i].trim();
        const cookieName = cookie.split('=')[0];
        
        if (cookieName.startsWith('_ga') || cookieName.startsWith('_gid') || cookieName.startsWith('_gat')) {
          document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        }
      }
      
      console.log('Google Analytics disabled and cookies removed');
    }
  }
};
