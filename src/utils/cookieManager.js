/**
 * Cookie Manager Utility
 * 
 * This utility provides functions for managing cookies in a privacy-focused way
 * that's compatible with Chrome's third-party cookie phase-out.
 */

// Cookie consent categories
export const COOKIE_CATEGORIES = {
  ESSENTIAL: 'essential',
  PREFERENCES: 'preferences',
  ANALYTICS: 'analytics',
  MARKETING: 'marketing'
};

// Default cookie settings
const DEFAULT_COOKIE_OPTIONS = {
  path: '/',
  sameSite: 'Lax', // Use 'Lax' as default for better privacy
  secure: window.location.protocol === 'https:' // Only use secure cookies on HTTPS
};

/**
 * Set a cookie with the specified name, value, and options
 * 
 * @param {string} name - Cookie name
 * @param {string} value - Cookie value
 * @param {Object} options - Cookie options
 * @param {number} options.maxAge - Cookie max age in seconds
 * @param {string} options.expires - Cookie expiration date
 * @param {string} options.path - Cookie path
 * @param {string} options.domain - Cookie domain
 * @param {boolean} options.secure - Whether the cookie should only be sent over HTTPS
 * @param {string} options.sameSite - Cookie SameSite attribute ('Strict', 'Lax', or 'None')
 * @param {string} options.category - Cookie category for consent management
 */
export const setCookie = (name, value, options = {}) => {
  // Merge default options with provided options
  const cookieOptions = { ...DEFAULT_COOKIE_OPTIONS, ...options };
  
  // Check if the user has consented to this cookie category
  const category = cookieOptions.category || COOKIE_CATEGORIES.ESSENTIAL;
  if (category !== COOKIE_CATEGORIES.ESSENTIAL && !hasConsentForCategory(category)) {
    console.log(`Cookie "${name}" not set because user hasn't consented to ${category} cookies`);
    return;
  }
  
  // Build the cookie string
  let cookieString = `${encodeURIComponent(name)}=${encodeURIComponent(value)}`;
  
  // Add cookie options
  if (cookieOptions.maxAge) {
    cookieString += `; Max-Age=${cookieOptions.maxAge}`;
  }
  
  if (cookieOptions.expires) {
    const expiresDate = typeof cookieOptions.expires === 'string' 
      ? new Date(cookieOptions.expires) 
      : cookieOptions.expires;
    cookieString += `; Expires=${expiresDate.toUTCString()}`;
  }
  
  if (cookieOptions.path) {
    cookieString += `; Path=${cookieOptions.path}`;
  }
  
  if (cookieOptions.domain) {
    cookieString += `; Domain=${cookieOptions.domain}`;
  }
  
  if (cookieOptions.secure) {
    cookieString += '; Secure';
  }
  
  if (cookieOptions.sameSite) {
    cookieString += `; SameSite=${cookieOptions.sameSite}`;
  }
  
  // Set the cookie
  document.cookie = cookieString;
};

/**
 * Get a cookie by name
 * 
 * @param {string} name - Cookie name
 * @returns {string|null} - Cookie value or null if not found
 */
export const getCookie = (name) => {
  const cookies = document.cookie.split(';');
  
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim();
    const [cookieName, cookieValue] = cookie.split('=');
    
    if (decodeURIComponent(cookieName) === name) {
      return decodeURIComponent(cookieValue || '');
    }
  }
  
  return null;
};

/**
 * Delete a cookie by name
 * 
 * @param {string} name - Cookie name
 * @param {Object} options - Cookie options (path, domain)
 */
export const deleteCookie = (name, options = {}) => {
  // Set expiration date to the past to delete the cookie
  setCookie(name, '', {
    ...options,
    expires: new Date(0)
  });
};

/**
 * Check if the user has consented to a specific cookie category
 * 
 * @param {string} category - Cookie category
 * @returns {boolean} - Whether the user has consented to the category
 */
export const hasConsentForCategory = (category) => {
  const consentData = getConsentData();
  return consentData.categories[category] === true;
};

/**
 * Get the user's consent data from localStorage
 * 
 * @returns {Object} - User's consent data
 */
export const getConsentData = () => {
  const consentDataString = localStorage.getItem('cookieConsent');
  
  if (!consentDataString) {
    return {
      consented: false,
      timestamp: null,
      categories: {
        [COOKIE_CATEGORIES.ESSENTIAL]: true, // Essential cookies are always allowed
        [COOKIE_CATEGORIES.PREFERENCES]: false,
        [COOKIE_CATEGORIES.ANALYTICS]: false,
        [COOKIE_CATEGORIES.MARKETING]: false
      }
    };
  }
  
  try {
    return JSON.parse(consentDataString);
  } catch (error) {
    console.error('Error parsing cookie consent data:', error);
    return {
      consented: false,
      timestamp: null,
      categories: {
        [COOKIE_CATEGORIES.ESSENTIAL]: true,
        [COOKIE_CATEGORIES.PREFERENCES]: false,
        [COOKIE_CATEGORIES.ANALYTICS]: false,
        [COOKIE_CATEGORIES.MARKETING]: false
      }
    };
  }
};

/**
 * Save the user's consent data to localStorage
 * 
 * @param {Object} consentData - User's consent data
 */
export const saveConsentData = (consentData) => {
  localStorage.setItem('cookieConsent', JSON.stringify(consentData));
};

/**
 * Update the user's consent for a specific category
 * 
 * @param {string} category - Cookie category
 * @param {boolean} consented - Whether the user has consented
 */
export const updateCategoryConsent = (category, consented) => {
  const consentData = getConsentData();
  
  consentData.categories[category] = consented;
  consentData.timestamp = new Date().toISOString();
  
  saveConsentData(consentData);
};

/**
 * Save the user's full consent preferences
 * 
 * @param {Object} categories - Object with category keys and boolean values
 */
export const saveFullConsent = (categories) => {
  const consentData = getConsentData();
  
  consentData.consented = true;
  consentData.timestamp = new Date().toISOString();
  consentData.categories = {
    ...consentData.categories,
    ...categories,
    [COOKIE_CATEGORIES.ESSENTIAL]: true // Essential cookies are always allowed
  };
  
  saveConsentData(consentData);
};
