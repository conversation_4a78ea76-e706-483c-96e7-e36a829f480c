/**
 * Draw a shape based on the page type
 * @param {CanvasRenderingContext2D} ctx - Canvas context
 * @param {string} shape - Shape type (services, portfolio, books, contact)
 * @param {number} width - Canvas width
 * @param {number} height - Canvas height
 * @param {string} color - Shape color
 * @param {Object} mousePosition - Current mouse position {x, y}
 */
export const drawShape = (ctx, shape, width, height, color, mousePosition) => {
  const centerX = width / 2;
  const centerY = height / 2;
  const size = Math.min(width, height) * 0.15; // Base size for shapes

  // Calculate glow intensity based on mouse position
  let glowIntensity = 0.3; // Base glow
  if (mousePosition.x !== null && mousePosition.y !== null) {
    const dx = mousePosition.x - centerX;
    const dy = mousePosition.y - centerY;
    const distance = Math.sqrt(dx * dx + dy * dy);
    const maxDistance = Math.sqrt(width * width + height * height) / 2;

    // Increase glow when mouse is closer to the center
    if (distance < maxDistance / 2) {
      glowIntensity = 0.3 + (0.6 * (1 - distance / (maxDistance / 2)));
    }
  }

  // Apply shadow for glow effect
  ctx.shadowBlur = 20;
  ctx.shadowColor = color;
  ctx.fillStyle = color + Math.floor(glowIntensity * 255).toString(16).padStart(2, '0');

  switch (shape) {
    case 'services':
      drawServicesShape(ctx, centerX, centerY, size, glowIntensity);
      break;
    case 'portfolio':
      drawPortfolioShape(ctx, centerX, centerY, size, glowIntensity);
      break;
    case 'books':
      drawBooksShape(ctx, centerX, centerY, size, glowIntensity);
      break;
    case 'contact':
      drawContactShape(ctx, centerX, centerY, size, glowIntensity);
      break;
    default:
      // No shape
      break;
  }

  // Reset shadow
  ctx.shadowBlur = 0;
};

/**
 * Draw a CPU/circuit shape for Services page
 */
// eslint-disable-next-line no-unused-vars
const drawServicesShape = (ctx, centerX, centerY, size, _glowIntensity) => {
  // Draw a CPU/circuit board shape
  const cpuSize = size * 0.8;
  const pinSize = size * 0.15;

  // CPU body
  ctx.beginPath();
  ctx.rect(centerX - cpuSize/2, centerY - cpuSize/2, cpuSize, cpuSize);
  ctx.fill();

  // CPU pins
  ctx.beginPath();
  // Top pins
  for (let i = 0; i < 5; i++) {
    const x = centerX - cpuSize/2 + (cpuSize/6) * (i+1);
    ctx.rect(x - pinSize/2, centerY - cpuSize/2 - pinSize, pinSize, pinSize);
  }
  // Bottom pins
  for (let i = 0; i < 5; i++) {
    const x = centerX - cpuSize/2 + (cpuSize/6) * (i+1);
    ctx.rect(x - pinSize/2, centerY + cpuSize/2, pinSize, pinSize);
  }
  // Left pins
  for (let i = 0; i < 5; i++) {
    const y = centerY - cpuSize/2 + (cpuSize/6) * (i+1);
    ctx.rect(centerX - cpuSize/2 - pinSize, y - pinSize/2, pinSize, pinSize);
  }
  // Right pins
  for (let i = 0; i < 5; i++) {
    const y = centerY - cpuSize/2 + (cpuSize/6) * (i+1);
    ctx.rect(centerX + cpuSize/2, y - pinSize/2, pinSize, pinSize);
  }
  ctx.fill();

  // CPU inner circle
  ctx.beginPath();
  ctx.arc(centerX, centerY, cpuSize * 0.3, 0, Math.PI * 2);
  ctx.fill();
};

/**
 * Draw a frame/window shape for Portfolio page
 */
// eslint-disable-next-line no-unused-vars
const drawPortfolioShape = (ctx, centerX, centerY, size, _glowIntensity) => {
  // Draw a browser window/frame shape
  const frameWidth = size * 1.4;
  const frameHeight = size;
  const headerHeight = frameHeight * 0.2;
  const radius = 10;

  // Draw rounded rectangle for browser window
  ctx.beginPath();
  ctx.moveTo(centerX - frameWidth/2 + radius, centerY - frameHeight/2);
  ctx.lineTo(centerX + frameWidth/2 - radius, centerY - frameHeight/2);
  ctx.arcTo(centerX + frameWidth/2, centerY - frameHeight/2, centerX + frameWidth/2, centerY - frameHeight/2 + radius, radius);
  ctx.lineTo(centerX + frameWidth/2, centerY + frameHeight/2 - radius);
  ctx.arcTo(centerX + frameWidth/2, centerY + frameHeight/2, centerX + frameWidth/2 - radius, centerY + frameHeight/2, radius);
  ctx.lineTo(centerX - frameWidth/2 + radius, centerY + frameHeight/2);
  ctx.arcTo(centerX - frameWidth/2, centerY + frameHeight/2, centerX - frameWidth/2, centerY + frameHeight/2 - radius, radius);
  ctx.lineTo(centerX - frameWidth/2, centerY - frameHeight/2 + radius);
  ctx.arcTo(centerX - frameWidth/2, centerY - frameHeight/2, centerX - frameWidth/2 + radius, centerY - frameHeight/2, radius);
  ctx.fill();

  // Draw browser header line
  ctx.beginPath();
  ctx.moveTo(centerX - frameWidth/2, centerY - frameHeight/2 + headerHeight);
  ctx.lineTo(centerX + frameWidth/2, centerY - frameHeight/2 + headerHeight);
  ctx.stroke();

  // Draw browser buttons
  const buttonRadius = 5;
  const buttonSpacing = 15;
  ctx.beginPath();
  ctx.arc(centerX - frameWidth/2 + 20, centerY - frameHeight/2 + headerHeight/2, buttonRadius, 0, Math.PI * 2);
  ctx.arc(centerX - frameWidth/2 + 20 + buttonSpacing, centerY - frameHeight/2 + headerHeight/2, buttonRadius, 0, Math.PI * 2);
  ctx.arc(centerX - frameWidth/2 + 20 + buttonSpacing * 2, centerY - frameHeight/2 + headerHeight/2, buttonRadius, 0, Math.PI * 2);
  ctx.fill();
};

/**
 * Draw a book shape for Books page
 */
// eslint-disable-next-line no-unused-vars
const drawBooksShape = (ctx, centerX, centerY, size, _glowIntensity) => {
  // Draw an open book shape
  const bookWidth = size * 1.2;
  const bookHeight = size * 0.8;
  const spineWidth = bookWidth * 0.1;

  // Left page
  ctx.beginPath();
  ctx.moveTo(centerX - spineWidth/2, centerY - bookHeight/2);
  ctx.lineTo(centerX - bookWidth/2, centerY - bookHeight/2 + bookHeight * 0.1);
  ctx.lineTo(centerX - bookWidth/2, centerY + bookHeight/2);
  ctx.lineTo(centerX - spineWidth/2, centerY + bookHeight/2);
  ctx.fill();

  // Right page
  ctx.beginPath();
  ctx.moveTo(centerX + spineWidth/2, centerY - bookHeight/2);
  ctx.lineTo(centerX + bookWidth/2, centerY - bookHeight/2 + bookHeight * 0.1);
  ctx.lineTo(centerX + bookWidth/2, centerY + bookHeight/2);
  ctx.lineTo(centerX + spineWidth/2, centerY + bookHeight/2);
  ctx.fill();

  // Book spine
  ctx.beginPath();
  ctx.moveTo(centerX - spineWidth/2, centerY - bookHeight/2);
  ctx.lineTo(centerX + spineWidth/2, centerY - bookHeight/2);
  ctx.lineTo(centerX + spineWidth/2, centerY + bookHeight/2);
  ctx.lineTo(centerX - spineWidth/2, centerY + bookHeight/2);
  ctx.fill();

  // Book lines (text representation)
  ctx.beginPath();
  for (let i = 0; i < 5; i++) {
    const y = centerY - bookHeight/3 + (bookHeight/2) * (i/5);
    // Left page lines
    ctx.moveTo(centerX - spineWidth/2 - bookWidth/3, y);
    ctx.lineTo(centerX - spineWidth/2 - bookWidth/10, y);
    // Right page lines
    ctx.moveTo(centerX + spineWidth/2 + bookWidth/10, y);
    ctx.lineTo(centerX + spineWidth/2 + bookWidth/3, y);
  }
  ctx.stroke();
};

/**
 * Draw a message/envelope shape for Contact page
 */
// eslint-disable-next-line no-unused-vars
const drawContactShape = (ctx, centerX, centerY, size, _glowIntensity) => {
  // Draw an envelope/message shape
  const envelopeWidth = size * 1.2;
  const envelopeHeight = size * 0.8;

  // Envelope body
  ctx.beginPath();
  ctx.rect(centerX - envelopeWidth/2, centerY - envelopeHeight/2, envelopeWidth, envelopeHeight);
  ctx.fill();

  // Envelope flap
  ctx.beginPath();
  ctx.moveTo(centerX - envelopeWidth/2, centerY - envelopeHeight/2);
  ctx.lineTo(centerX, centerY);
  ctx.lineTo(centerX + envelopeWidth/2, centerY - envelopeHeight/2);
  ctx.fill();

  // Envelope line
  ctx.beginPath();
  ctx.moveTo(centerX - envelopeWidth/2, centerY - envelopeHeight/2);
  ctx.lineTo(centerX, centerY);
  ctx.lineTo(centerX + envelopeWidth/2, centerY - envelopeHeight/2);
  ctx.stroke();
};
