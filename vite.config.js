import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
// import imagemin from 'vite-plugin-imagemin'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // Temporarily disabled imagemin for development
    /* imagemin({
      gifsicle: {
        optimizationLevel: 7,
        interlaced: false,
      },
      optipng: {
        optimizationLevel: 5, // Further reduced from 6 to preserve color fidelity
      },
      mozjpeg: {
        quality: 90, // Increased from 85 to better preserve color fidelity
        progressive: true,
        // Preserve color profile
        dcScanOpt: 2,
        colorspace: 'rgb',
        quantTable: 3,
        trellis: true, // Enable trellis optimization for better quality
        trellisDC: true, // Apply trellis optimization to DC coefficients
        overshoot: true // Allow overshooting of samples for better color reproduction
      },
      pngquant: {
        quality: [0.85, 0.95], // Increased from [0.8, 0.9] for better color fidelity
        speed: 1, // Reduced from 2 to maximize quality (1 is highest quality)
        strip: false, // Don't strip color profiles
        dithering: 1.0, // Increased dithering to maximum for better color transitions
        posterize: false, // Don't posterize colors
        floyd: 1.0 // Use Floyd-<PERSON> dithering at maximum quality
      },
      svgo: {
        plugins: [
          {
            name: 'removeViewBox',
            active: false,
          },
          {
            name: 'removeEmptyAttrs',
            active: false,
          },
        ],
      },
      webp: {
        quality: 90, // Increased from 80 for better color fidelity
        alphaQuality: 100, // Maximum alpha channel quality
        method: 6, // Highest quality encoding method (0-6)
        exact: true, // Preserve color exactness
        lossless: false, // Use lossy compression but with high quality
        nearLossless: true, // Use near-lossless preprocessing
        smartSubsample: true, // Preserve color detail in chroma channels
      },
      // Only process image files with specific extensions and exclude problematic files
      filter: (file) => {
        // Get the file extension
        const ext = path.extname(file).toLowerCase();

        // Define image extensions to process
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];

        // Check if the file has an image extension
        const hasImageExtension = imageExtensions.includes(ext);

        // If not an image file, don't process it
        if (!hasImageExtension) {
          // Skip non-image files silently
          return false;
        }

        // Get the filename
        const filename = path.basename(file);

        // Exclude patterns for all problematic files (handles both original and hashed filenames)
        const excludePatterns = [
          // Large image files
          'Hrishikesh-Mohite-predictive-analytics-dashboard',
          'Hrishikesh-Mohite-nlp-api',
          'Hrishikesh-Mohite-Neural-Network-Playground',
          'Hrishikesh-Mohite-professional-portrait',

          // Additional files with imagemin errors
          'Hrishikesh-Mohite-mobile-app-ui-design',
          'Hrishikesh-Mohite-CareerCraft',
          'Hrishikesh-Mohite-product-explainer-video',
          'Hrishikesh-Mohite-control-pride-book',
          'Hrishikesh-Mohite-symphony-of-stars-book',
          'Hrishikesh-Mohite-time-to-show-book',
          'Hrishikesh-Mohite-love-through-adversity-book',
          'Hrishikesh-Mohite-entrepreneur-mindset-book',
          'Hrishikesh-Mohite-3d-product-visualization',
          'Hrishikesh-Mohite-corporate-brand-video'
        ];

        // Check if the filename contains any of the exclude patterns
        const shouldExclude = excludePatterns.some(pattern => filename.includes(pattern));

        // For excluded files, we could log them in development mode if needed
        // if (shouldExclude && process.env.NODE_ENV !== 'production') {
        //   console.log(`Imagemin: Skipping excluded file: ${filename}`);
        // }

        // Return false for excluded files, true for other image files
        return !shouldExclude;
      }
    }), */
  ],
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Create a chunk for React and related packages
          if (id.includes('node_modules/react') ||
              id.includes('node_modules/react-dom') ||
              id.includes('node_modules/react-router-dom')) {
            return 'react-vendor';
          }

          // Create a chunk for UI components
          if (id.includes('node_modules/framer-motion') ||
              id.includes('node_modules/styled-components') ||
              id.includes('node_modules/react-icons')) {
            return 'ui-components';
          }

          // Let Vite handle other dependencies
          if (id.includes('node_modules')) {
            return 'vendor';
          }
        }
      },
    },
    chunkSizeWarningLimit: 1000,
  },
})
